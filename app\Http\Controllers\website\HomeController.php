<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Models\adminData;
use App\Models\contact;
use App\Models\countryAndLocations;
use App\Models\customTrips;
use App\Models\DestinationBooking;
use App\Models\DestinationCategory;
use App\Models\Destinations;
use App\Models\destinationSlots;
use App\Models\DestinationType;
use App\Models\newsletter;
use App\Models\pagesSeo;
use App\Models\paymentLinks;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Commissions;
use Illuminate\Auth\Events\Registered;
use App\Models\Reviews;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use NZTim\Mailchimp\Mailchimp;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class HomeController extends Controller
{
    protected $mailchimp;

    public function __construct(Mailchimp $mailchimp)
    {
        $this->mailchimp = $mailchimp;
    }

    // public function comming()
    // {
    //   return view('front.comming_soon');
    //}

    public function index()
    {
        $seoData = pagesSeo::where('page', 'home')->first();
        $destinationType = DestinationType::orderBy('created_at', 'desc')->get();
        $reviews = Reviews::where('status', 1)->orderBy('created_at', 'desc')->get();
        $destinationCategory = DestinationCategory::orderBy('created_at', 'desc')->get();
        $popularDestination = Destinations::where('status', 1)->where('popular', 1)->orderBy('created_at', 'desc')->get();
        $trendingDestination = Destinations::where('status', 1)->where('trending', 1)->orderBy('created_at', 'desc')->get();
        $destinationCountryLocation = countryAndLocations::all();
        return view('website.index', [
            'destinationType' => $destinationType,
            'reviews' => $reviews,
            'destinationCategory' => $destinationCategory,
            'popularDestination' => $popularDestination,
            'trendingDestination' => $trendingDestination,
            'destinationCountryLocation' => $destinationCountryLocation,
            'seoData' => $seoData,
        ]);
    }

    public function liveSearch(Request $request)
    {
        if ($request->ajax()) {
            $query = $request->input('query');
            $destination = Destinations::where('title', 'LIKE', "%{$query}%")->orWhere('destination_country', 'LIKE', "%{$query}%")->get();

            return response()->json($destination);
        }
    }


    public function about()
    {
        $seoData = pagesSeo::where('page', 'contact')->first();
        $reviews = Reviews::where('status', 1)->orderBy('created_at', 'desc')->get();
        return view('website.about', [
            'reviews' => $reviews,
            'seoData' => $seoData,

        ]);
    }
    public function contact()
    {
        $seoData = pagesSeo::where('page', 'contact')->first();
        return view('website.contact', [
            'seoData' => $seoData,

        ]);
    }


    public function privacyPolicy()
    {
        $seoData = pagesSeo::where('page', 'privacyPolicy')->first();
        return view('website.privacyPolicy', [
            'seoData' => $seoData,

        ]);
    }
    public function travelGuide()
    {
        $seoData = pagesSeo::where('page', 'travelGuide')->first();
        return view('website.travelGuide', [
            'seoData' => $seoData,

        ]);
    }
    public function termsAndConditions()
    {
        $seoData = pagesSeo::where('page', 'termsAndConditions')->first();
        return view('website.termsAndConditions', [
            'seoData' => $seoData,

        ]);
    }
    public function bookingTermsAndConditions()
    {
        $seoData = pagesSeo::where('page', 'bookingTermsAndConditions')->first();
        return view('website.bookingTermsAndConditions', [
            'seoData' => $seoData,

        ]);
    }




    public function getAgent()
    {
        $seoData = pagesSeo::where('page', 'agent')->first();
        return view('website.agent', [
            'seoData' => $seoData,

        ]);
    }
    public function subscribe($selectedPackage)
    {
        $seoData = pagesSeo::where('page', 'subscribe')->first();
        $country = Commissions::all();
        $subscriptions = Subscription::where('status', 1)->get();

        $user = auth()->user();
        if ($user && $user->email_verified_at) {
            return view('website.subscribe', ['subscriptions' => $subscriptions, 'selectedPackage' => $selectedPackage, 'seoData' => $seoData, 'country' => $country]);
        } else {
            return redirect()->route('verification.notice')->with('error', 'Please verify your email address.');
        }
    }

    public function showDestinations(Request $request)
    {
        $seoData = pagesSeo::where('page', 'destination')->first();
        $query = Destinations::where('status', 1);
        $destinationCategory = DestinationCategory::all();
        $destinationType = DestinationType::all();
        // Apply sorting
        switch ($request->input('sort_by')) {
            case 'price_high_to_low':
                $query->orderBy('price', 'desc');
                break;
            case 'price_low_to_high':
                $query->orderBy('price', 'asc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Apply price range filter
        if ($request->has('price_range')) {
            $priceRange = explode(' - ', $request->input('price_range'));
            $minPrice = (int) $priceRange[0];
            $maxPrice = (int) $priceRange[1];
            $query->whereBetween('final_price', [$minPrice, $maxPrice]);
        }

        // $destinations = $query->get();
        $destinations = $query->paginate(20);
        //dd($destinations);
        return view('website.destinations', [
            'destinations' => $destinations,
            'destinationCategory' => $destinationCategory,
            'destinationType' => $destinationType,
            'seoData' => $seoData,

        ]);
    }
    public function destinationDetail($slug)
    {
        $destinationDetail = Destinations::where('slug', $slug)->firstOrFail();
        $relatedDestinations = Destinations::where('category_id', $destinationDetail->category_id)
            ->where('id', '!=', $destinationDetail->id)->where('status', 1)->take(10)
            ->get();

        if ($destinationDetail) {
            // Retrieve all dates associated with the destination
            $datesData = json_decode($destinationDetail->departure_date_time);
            $dates = explode(', ', $datesData);

            // Initialize an empty array to store slots
            $slotsData = [];

            foreach ($dates as $date) {

                // Retrieve slots for each date
                $dateSlots = destinationSlots::where('destination_id', $destinationDetail->id)
                    ->where('date', customDate($date, 'F d, Y'))
                    ->first();

                // Store the slots data in the array
                $slotsData[$date] = [
                    'date' => customDate($date, 'F d, Y'),
                    'book_slots' => $dateSlots ? $dateSlots->book_slots : null,
                    'slots' => $dateSlots ? $dateSlots->slots : null,
                ];
            }
            $subscriber = $destinationDetail->user->subscriber;
             $user = $destinationDetail->user;
        }

        return view('website.destinationDetail', ['user'=>$user, 'destinationDetail' => $destinationDetail, 'relatedDestinations' => $relatedDestinations, 'slotsData' => $slotsData, 'subscriber' => $subscriber]);
    }

    public function contactSubmit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'g-recaptcha-response' => 'required|recaptcha',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $contact = contact::create($request->all());

        $admin = getAdmin();
        $adminData = adminData::first();

        sendMail([
            'view' => 'emailTemplates.contact',
            'to' => $adminData['contact_email'],
            // 'recipient_name' => $admin['name'],
            'subject' => 'Contact',
            'data' => [
                'from_name' => $contact->name,
                'from_email' => $contact->email,
                'from_phone' => $contact->phone,
                'message' => $contact->message,
            ],
        ]);

        return response()->json(['success' => 'Thank you for contacting us! Your message has been successfully submitted. We will get back to you shortly.', 'route' => route('home')]);
    }
    public function newsletterSubmit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'g-recaptcha-response' => 'required|recaptcha',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $existingNewsletter = newsletter::where('email', $request->email)->first();

        if ($existingNewsletter) {
            $response = [
                'error' => 'Email already subscribed to our newsletter.',
                'status' => 'error',
                'route' => route('home'),
            ];

            return response()->json($response, 403);
        }

        $newsletter = newsletter::create($request->all());

        // Now you can use the Mailchimp class in this file

        $listId = env('MAILCHIMP_NEWSLETTER_LIST_ID');
        try {
            $this->mailchimp->subscribe($listId, $request->email, $merge = [], $confirm = false);
            // $news = newsletter::create($request->all());

        } catch (\Exception $e) {

            $response = [
                'error' => $e->getMessage(),
                'status' => 'error',
                'route' => route('home'),
            ];

            return response()->json($response);
            dd($e->getMessage());
        }

        $admin = getAdmin();
        $adminData = adminData::first();
        sendMail([
            'view' => 'emailTemplates.admin_newsletter_template',
            'to' => $adminData['newsletter_email'],
            'subject' => 'Newletter Subscription',
            'data' => [
                'email' => $newsletter->email,
            ],
        ]);

        sendMail([
            'view' => 'emailTemplates.user_newsletter_template',
            'to' => $newsletter->email,
            'subject' => 'Newletter Subscription',
            'data' => [
                'email' => $newsletter->email,
            ],
        ]);

        $response = [
            'success' => 'Thank you for subscribing our newsletter.',
            'status' => 'success',
            'route' => route('home'),
        ];

        return response()->json($response);
    }

    public function getDetinationTypeList()
    {
        $seoData = pagesSeo::where('page', 'destinationType')->first();
        $destinationTypes = DestinationType::get();
        return view('website.destinationType.destinationTypes', ['destinationTypes' => $destinationTypes, 'seoData' => $seoData]);
    }
    public function getDetinationTypeDetail($slug)
    {
        $type = DestinationType::where('slug', $slug)->firstOrFail();
        // Fetch destinations for this type
        $destinations = $type->destinations()->where('status', 1)->orderBy('created_at', 'desc')->paginate(20);
        // $destinations = Destinations::where('slug', $slug)->where('status', 1)->orderBy('created_at', 'desc')->paginate(20);

        return view('website.destinationType.destinationTypesDetail', ['destinations' => $destinations, 'type' => $type]);
    }

    public function getDetinationCategoryList()
    {
        $seoData = pagesSeo::where('page', 'destinationCategory')->first();
        $destinationCategory = DestinationCategory::get();

        return view('website.destinationCategory.destinationCategory', ['destinationCategory' => $destinationCategory, 'seoData' => $seoData]);
    }
    public function getDetinationCategoryDetail($slug)
    {
        $category = DestinationCategory::where('slug', $slug)->firstOrFail();
        // Fetch destinations for this type
        $destinations = $category->destinations()->where('status', 1)->orderBy('created_at', 'desc')->paginate(20);
        // $destinations = Destinations::where('slug', $slug)->where('status', 1)->orderBy('created_at', 'desc')->paginate(20);

        return view('website.destinationCategory.destinationCategoryDetail', ['destinations' => $destinations, 'category' => $category]);
    }

    private function handleUserAuthentication(Request $request)
    {
        $email = $request->email;

        if (!$email) {
            return null;
        }

        $user = User::where('email', $email)->first();

        if ($user) {
            Auth::login($user); // Login existing user
            return $user;
        }

        // Register new user
        $password = Str::random(10);

        $user = new User();
        $user->name = $request->name ?? 'Guest User';
        $user->email = $email;
        $user->password = Hash::make($password);
        $user->role = 'user';
        $user->save();

        event(new Registered($user));
        Auth::login($user);

        return $user;
    }


    public function DestinationBooking(Request $request)
    {
     

        $validator = Validator::make($request->all(), [
            'date' => 'required',
            'tickets' => 'required|integer|min:1',
            'adultes' => 'required|integer|min:0',
            'children' => 'required|integer|min:0',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $accountCreated = false;

        // If user not logged in, try to authenticate/register via email
        if (!auth()->check()) {
            if ($request->has('email')) {
                $existingUser = User::where('email', $request->email)->exists();
                $user = $this->handleUserAuthentication($request);
                if (!$user) {
                    return response()->json(['error' => 'Email is required for booking.'], 401);
                } else {
                    if (!$existingUser) {
                        $accountCreated = true;
                    }
                }
            } else {
                return response()->json(['error' => 'Login with your account to submit booking request.'], 401);
            }
        }





        if ($request->type !== 'private') {

            if ($request->tickets > $request->slots) {
                return response()->json(['error' => 'Tickets must be less than or equal to slots'], 402);
            }
        }


        if ((int) $request->adultes + (int) $request->children !== (int) $request->tickets) {
            return response()->json(['error' => 'Adultes + Children must be equal to Tickets'], 403);
        }



        $bookingData = $request->all();

        $bookingData['user_id'] = auth()->id();
        if (isset($request->extras)) {
            $extras = implode(',', $request->input('extras'));
            $bookingData['extras'] = $extras;
        }

        // Check if the 'terms' checkbox is checked
        $bookingData['terms'] = $request->has('terms') ? 1 : 0;

        $booking = DestinationBooking::create($bookingData);
        $booking->phone = $request->full_phone_number;


        $destination = Destinations::findOrFail($request->destination_id);
       
        if ($destination->flag !== 1) {

            if ($request->tickets == '1') {
                $booking->single_price = $destination->final_price;
            } elseif ($request->tickets == '2') {
                $booking->single_price = $destination->two_final_price;
            } elseif ($request->tickets == '3' || $request->tickets == '4') {
                $booking->single_price = $destination->three_to_four_final_price;
            } elseif ($request->tickets == '5' || $request->tickets == '6') {
                $booking->single_price = $destination->five_to_six_final_price;
            } elseif ($request->tickets > '6') {
                $booking->single_price = $destination->six_plus_final_price;
            }
        } else {

            $booking->single_price = $destination->final_price;
        }

        $booking->save();

        $userBookingData = User::where('id', auth()->id())->first();

        $userData = User::where('id', $request->destination_owner_user_id)->first();

        if ($userData->role != 'admin') {

            sendMail([
                'view' => 'emailTemplates.destination_booking_request_template',
                'to' => $userData['email'],
                // 'recipient_name' => $admin['name'],
                'subject' => 'Booking Request',
                'data' => [
                    'name' => $userBookingData->name,
                    'email' => $userBookingData->email,
                    'phone' => $booking->full_phone_number,
                    'date' => $booking->date,
                ],
            ]);
        } else {

            $adminData = adminData::first();
            sendMail([
                'view' => 'emailTemplates.destination_booking_request_template',
                'to' => $adminData['primary_email'],
                // 'recipient_name' => $admin['name'],
                'subject' => 'Booking Request',
                'data' => [
                    'name' => $userBookingData->name,
                    'email' => $userBookingData->email,
                    'phone' => $booking->full_phone_number,
                    'date' => $booking->date,
                ],
            ]);
        }


        $successMessage = 'Booking request submitted successfully.';
        if ($accountCreated) {
            $successMessage .= 'and new account has been created with your email.';
        }
        $successMessage .= ' Our team will contact you within 24 hours.';
        return response()->json(['success' => $successMessage, 'route' => route('home')]);
    }

    public function customTripRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [

            //'city' => 'required',
            'country' => 'required',
            //'destination_list' => 'required',

        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        if (!auth()->check()) {
            return response()->json(['error' => 'login with your account to submit booking request.'], 401);
        }
        $customTrips = new customTrips();
        $customTrips->user_id = auth()->id();
        $customTrips->phone = $request->full_phone_number;
        $customTrips->total_participant = $request->total_participant;
        $customTrips->total_adults = $request->total_adult;
        $customTrips->total_children = $request->total_children;
        $customTrips->total_days = $request->total_days;
        $customTrips->dates = $request->from_to_date;
        $customTrips->message = $request->message;

        if (isset($request->country)) {

            $destinationCountry = implode(',', $request->input('country'));
            $customTrips->country = $destinationCountry;
        }
        //    if (isset($request->city)) {
        //        $destinationCities = implode(',', $request->input('city'));
        //        $customTrips->city = $destinationCities;
        //   }

        //    if (isset($request->destination_list)) {
        //        $destinationDetail = implode(',', $request->input('destination_list'));
        //        $customTrips->areas = $destinationDetail;
        //    }

        $customTrips->save();

        $adminData = adminData::first();
        sendMail([
            'view' => 'emailTemplates.custom_booking_request_template',
            'to' => $adminData['primary_email'],
            'subject' => 'Custom Tour Request',
            'data' => [
                'from_name' => auth()->user()->name,
                'from_phone' => $request->number,
                'message' => $request->message,
            ],
        ]);

        return response()->json(['success' => 'booking request submit success', 'route' => route('home')]);
    }

    public function customTripPaymentView(Request $request)
    {
        $token = $request->query('token');
        $data = json_decode(base64_decode($request->query('data')), true);

        $payment_link = paymentLinks::where('token', $token)->first();
        $expiry_time = $payment_link->expiry_at;

        $id = $data['id'] ?? null;
        $userId = $data['user_id'] ?? null;
        $price = $data['price'] ?? null;
        $paymentType = $data['payment_type'];
        $tripType = $data['trip_type'];
        $user = User::where('id', $userId)->first();
        $name = $user->name;
        $email = $user->email;

        if (now()->gt($expiry_time)) {
            // Link has expired
            return view('website.customTripPayment.linkExpire', compact('id', 'name', 'email'));
        }

        return view('website.customTripPayment.customTripPayment', compact('id', 'price', 'userId', 'paymentType', 'tripType'));
    }

    public function getCountryCode()
    {
        // Fetch the user's IP address
        $ip = request()->ip();

        // Fetch geolocation data from ip-api
        $response = file_get_contents("http://ip-api.com/json/{$ip}");
        $data = json_decode($response, true);

        // Return the country code
        return response()->json(['countryCode' => $data['countryCode'] ?? 'US']); // Default to 'US' if country code not found
    }
}
