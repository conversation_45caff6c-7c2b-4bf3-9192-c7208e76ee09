<?php

namespace App\Http\Controllers;

use App\Models\adminData;
use App\Models\bookingPayments;
use App\Models\customTrips;
use App\Models\DestinationBooking;
use App\Models\Destinations;
use App\Models\newsletter;
use App\Models\pagesSeo;
use App\Models\paymentLinks;
use App\Models\countryAndLocations;
use App\Models\User;
use App\Models\Reviews;
use App\Models\Commissions;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class dashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        if ($user->role == 'admin') {
            $destinations = Destinations::with('user')->get();
            $destinationsBooking = DestinationBooking::with('destination')->get();
            return view('dashboard.index', ['destinations' => $destinations, 'destinationsBooking' => $destinationsBooking]);
        } else {
            $destinationsCount = Destinations::where('user_id', $user->id)->with('user')->get();
            $destinationsActiveCount = Destinations::where('user_id', $user->id)->where('status', '1')->with('user')->get();
            return view('dashboard.agentIndex', ['destinationsCount' => $destinationsCount, 'destinationsActiveCount' => $destinationsActiveCount]);
        }

    }

    public function websiteSettings()
    {
        $admindata = adminData::first();
        return view('dashboard.settings', ['admindata' => $admindata]);

    }



    public function commissionSetting()
    {
        
       $commissionData = Commissions::all();
        return view('dashboard.commission.index', ['commissionData' => $commissionData]);
    }
    public function reviewsList()
    {
        
       $reviews = Reviews::all();
        return view('dashboard.reviews.index', ['reviews' => $reviews]);
    }
       public function reviewDelete($id)
    {
      $review = Reviews::findOrFail($id);
        $review->delete();
        return response()->json(null, 200);
    }
    public function reviewStatusUpdate($id)
    {
      
        $reivew = Reviews::findOrFail($id);
        $reivew->status = !$reivew->status; // Toggle status
        $reivew->save();
        return response()->json($reivew, 200);
    }
    
    
     public function commissionAdd(Request $request)
    {
        
       $commission = new Commissions();
        $commission->country = $request->country;
        $commission->commission = $request->commission;
       

        if (Commissions::where('country', $request->country)->exists()) {
            return response()->json(['message' => 'already exists'], 422);
        }
        $commission->save();
        return response()->json($commission, 200);
     
    }
      public function commissionDelete($id)
    {
          $commission = Commissions::findOrFail($id);
        $commission->delete();
        return response()->json(null, 200);
    }
       public function commissionUpdate(Request $request, $id)
    {
        $commission = Commissions::findOrFail($id);
        if ($request->country !== $commission->country && Commissions::where('country', $request->country)->exists()) {
            return response()->json(['message' => 'already exists'], 422);
        }
       $commission->country = $request->country;
        $commission->commission = $request->commission;
        $commission->save();
        return response()->json($commission, 200);
    }



    public function websiteSEOSettings()
    {
        $seoData = pagesSeo::all();
        return view('dashboard.seo.seoSettings', ['seoData' => $seoData]);
    }
    public function addSeoData(Request $request)
    {

        $pageSeo = new pagesSeo();
        $pageSeo->page = $request->page;
        $pageSeo->page_h1_heading = $request->page_h1_heading;
        $pageSeo->meta_title = $request->meta_title;
        $pageSeo->meta_description = $request->meta_description;
        $pageSeo->meta_keywords = $request->meta_keywords;

        if (pagesSeo::where('page', $request->page)->exists()) {
            return response()->json(['message' => 'page with the same name already exists'], 422);
        }
        $pageSeo->save();
        return response()->json($pageSeo, 200);
    }

    public function updateSeoData(Request $request, $id)
    {
        $pageSeo = pagesSeo::findOrFail($id);
        if ($request->page !== $pageSeo->page && pagesSeo::where('page', $request->page)->exists()) {
            return response()->json(['message' => 'A page with the same name already exists'], 422);
        }
        $pageSeo->page = $request->page;
        $pageSeo->page_h1_heading = $request->page_h1_heading;
        $pageSeo->meta_title = $request->meta_title;
        $pageSeo->meta_description = $request->meta_description;
        $pageSeo->meta_keywords = $request->meta_keywords;
        $pageSeo->save();
        return response()->json($pageSeo, 200);
    }

    public function deleteSeoData($id)
    {
        $seo = pagesSeo::findOrFail($id);
        $seo->delete();
        return response()->json(null, 200);
    }

    public function updateGlobalData(Request $request, $id)
    {

        $adminData = adminData::findOrFail($id);

        $adminData->name = $request->name;

        $adminData->primary_email = $request->primary_email;
        $adminData->newsletter_email = $request->newsletter_email;
        $adminData->contact_email = $request->contact_email;
        $adminData->commission_percentage = $request->commission;
        $adminData->facebook = $request->facebook;
        $adminData->twitter = $request->twitter;
        $adminData->tiktok = $request->tiktok;
        $adminData->youtube = $request->youtube;
        $adminData->instagram = $request->instagram;
        $adminData->primary_no = $request->primary_phone_num;
        $adminData->secondary_no = $request->secondary_phone_num;
        $adminData->address = $request->address;

        $adminData->visible_recaptcha_sitekey = $request->visible_recaptcha_sitekey;
        $adminData->invisible_recaptcha_sitekey = $request->invisible_recaptcha_sitekey;

        $adminData->save();

        return response()->json($adminData, 200);

    }
    public function getNewsletter()
    {
        $newsletter = newsletter::all();
        return view('dashboard.newsletter.newsletterRecord', ['newsletter' => $newsletter]);

    }
    public function deleteNewsletter($id)
    {
        $contact = newsletter::findOrFail($id);
        $contact->delete();
        return response()->json(null, 200);
    }
    public function customTripList()
    {

        $customTrip = customTrips::with('user')->get();
        return view('dashboard.customTripRequest.customTripRequest', ['customTrip' => $customTrip]);

    }
    
     public function customTripListUpdateView($id)
    {
        $custom = customTrips::findOrFail($id);
        $destinationCountryLocation = countryAndLocations::all();
        return view('dashboard.customTripRequest.updateCustomTrip', compact('custom','destinationCountryLocation'));

    }
          public function updatecustomTripList(Request $request, $id)
    {
        $customTrips = customTrips::findOrFail($id);
        
        $customTrips->total_participant = $request->total_participant;
        $customTrips->total_adults = $request->total_adults;
        $customTrips->total_children = $request->total_children;
        $customTrips->total_days = $request->total_days;
        $customTrips->dates = $request->from_to_date;
        $customTrips->total_rooms = $request->total_rooms;
        
        if (isset($request->status)) {
            
            $customTrips->status = $request->input('status');
          
        }

        if (isset($request->country)) {
            
            $destinationCountry = implode(',', $request->input('country'));
            $customTrips->country = $destinationCountry;
        }
        
        if (isset($request->city)) {
            $destinationCities = implode(',', $request->input('city'));
            $customTrips->city = $destinationCities;
       }

        if (isset($request->destination_list)) {
            $destinationDetail = implode(',', $request->input('destination_list'));
            $customTrips->areas = $destinationDetail;
        }
        
          $customTrips->save();

        return response()->json($customTrips, 200);
    }
    
    
    public function customTripDetail($id)
    {
        $customTrip = customTrips::where('id', $id)->with('user')->first();
        $paymentDetails = bookingPayments::Where('custom_trip_id', $id)->get();
        return view('dashboard.customTripRequest.customTripDetail', ['customTrip' => $customTrip, 'paymentDetails' => $paymentDetails]);
    }

    public function deleteCustomTrip($id)
    {
        $customTrip = customTrips::findOrFail($id);
        $customTrip->delete();
        return response()->json(null, 200);
    }

    public function destinationBooking(request $request)
    {

        $status = $request->query('status');

        if ($status) {
            $BookingsData = DestinationBooking::where('status', $status)
                ->with('user', 'destination', 'bookedBy')
                ->get();
        }
        // elseif ($request->id) {
        //     $destination_ids = Destinations::where('user_id', $request->id)->pluck('id');
        //     $BookingsData = DestinationBooking::with('destination', 'bookedBy')->whereIn('destination_id', $destination_ids)->get();
        // }
        elseif (Auth::check()) {
            $user = Auth::user();

            $destination_ids = Destinations::where('user_id', $user->id)->pluck('id');
            $BookingsData = DestinationBooking::with('destination', 'bookedBy')->whereIn('destination_id', $destination_ids)->get();

            // $BookingsData = DestinationBooking::with('user', 'destination', 'bookedBy')->get();

            if ($user->role === 'admin') {
                $nonAdminUserIds = User::where('role', '!=', 'admin')->pluck('id');
                $destination_ids = Destinations::whereIn('user_id', $nonAdminUserIds)->pluck('id');

                $otherAgentBookingsData = DestinationBooking::with('destination', 'bookedBy')->whereIn('destination_id', $destination_ids)->get();

                return view('dashboard.destinationBooking.destinationBooking', ['BookingsData' => $BookingsData, '$otherAgentBookingsData' => $otherAgentBookingsData]);
            }

        }

        return view('dashboard.destinationBooking.destinationBooking', ['BookingsData' => $BookingsData]);
    }

    public function deleteDestinationBooking($id)
    {
        $tripBooking = DestinationBooking::findOrFail($id);
        $tripBooking->delete();
        return response()->json(null, 200);
    }

    public function customTripCancleReply(request $request, $id)
    {
        $customTripdata = customTrips::where('id', $request->id)->first();
        $userData = User::where('id', $customTripdata->user_id)->first();

        $responce = sendMail([
            'view' => 'emailTemplates.custom_trip_cancle_reply_template',
            'to' => $userData->email,
            'subject' => 'Cancel Custom Trip Request',
            'data' => [
                'reply' => $request->message,

            ],
        ]);
        $customTripdata->status = 'cancel';
        $customTripdata->save();

        return response()->json($responce, 200);
    }
    public function customTripAcceptReply(request $request, $id)
    {
        $customTripdata = customTrips::where('id', $request->id)->first();
        $userData = User::where('id', $customTripdata->user_id)->first();
        $customTripdata->total_price = $request->total_price;
        $customTripdata->advance_price = $request->advance_price;
        $customTripdata->status = 'advancePaymentPending';

        if ($request->hasFile('destination_detail')) {
            $file = $request->file('destination_detail');
            $destinationPdfPath = media($file, 'storage/customTrip');
            $destinationPdfPath = basename($destinationPdfPath);
            $customTripdata->destination_details = $destinationPdfPath;
        }
        $customTripdata->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->custom_trip_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);
        $paymentLink->save();

        $customTripdata = customTrips::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.custom_trip_accept_reply_template',
            'to' => $userData->email,
            'subject' => 'Advance Payment Link',
            'data' => [
                'reply' => $request->message,
                'total_price' => $request->total_price,
                'advance_percentage' => $request->advance_percentage,
                'advance_price' => $request->advance_price,
                'destination_detail' => $customTripdata->destination_details,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $customTripdata->id, 'user_id' => $customTripdata->user_id, 'price' => $customTripdata->advance_price, 'payment_type' => 'advance', 'trip_type' => 'custom'])),

            ],
        ]);

        return response()->json($responce, 200);
    }

    public function customTripCompletePayment(request $request, $id)
    {
        $customTripdata = customTrips::where('id', $request->id)->first();
        $userData = User::where('id', $customTripdata->user_id)->first();
        $customTripdata->total_price = $request->total_price;

        $customTripdata->status = 'completePaymentPending';

        if ($request->hasFile('destination_detail')) {
            $file = $request->file('destination_detail');
            $destinationPdfPath = media($file, 'storage/customTrip');
            $destinationPdfPath = basename($destinationPdfPath);
            $customTripdata->destination_details = $destinationPdfPath;
        }
        $customTripdata->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->custom_trip_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);
        $paymentLink->save();

        $customTripdata = customTrips::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.custom_trip_complete_payment_template',
            'to' => $userData->email,
            'subject' => 'Booking Payment Link',
            'data' => [
                'reply' => $request->message,
                'total_price' => $request->total_price,
                'destination_detail' => $customTripdata->destination_details,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $customTripdata->id, 'user_id' => $customTripdata->user_id, 'price' => $request->total_price, 'payment_type' => 'complete', 'trip_type' => 'custom'])),

            ],
        ]);

        return response()->json($responce, 200);
    }

    public function customTripRemainingPayment(request $request, $id)
    {
        $customTripdata = customTrips::where('id', $request->id)->first();
        $userData = User::where('id', $customTripdata->user_id)->first();

        $customTripdata->status = 'remainingPaymentPending';

        $customTripdata->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->custom_trip_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);
        //$paymentLink->expiry_at = now()->addMinutes(5);
        $paymentLink->save();

        $customTripdata = customTrips::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.custom_trip_remaining_payment_template',
            'to' => $userData->email,
            'subject' => 'Remaining Payment Link',
            'data' => [
                'reply' => $request->message,
                'total_price' => $customTripdata->total_price,
                'advance_price' => $customTripdata->advance_price,
                'remaining_price' => $request->remaining_price,
                'destination_detail' => $customTripdata->destination_details,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $customTripdata->id, 'user_id' => $customTripdata->user_id, 'price' => $request->remaining_price, 'payment_type' => 'remaining', 'trip_type' => 'custom'])),

            ],
        ]);

        return response()->json($responce, 200);
    }

    public function customTripPaymentList()
    {
        $customTripPayment = bookingPayments::with('user')
            ->where('trip_type', 'custom')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.customTripPayments.customTripPayments', ['customTripPayment' => $customTripPayment]);
    }

    public function bookingTripPaymentList()
    {
        $user = Auth::user();

        $bookingTripPayments = BookingPayments::with(['bookedDestination.destination'])
            ->whereHas('bookedDestination.destination', function ($query) use ($user) {
                $query->where('user_id', $user->id)->where('trip_type', 'booking');
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.destinations.bookingTripPayments', ['bookingTripPayments' => $bookingTripPayments]);
    }

    public function bookingAdvancePayment(request $request, $id)
    {
        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $userData = User::where('id', $bookingData->user_id)->first();

        $destinationData = Destinations::where('id', $bookingData->destination_id)->first();
        $bookingData->status = 'advancePaymentPending';

        if ($request->hasFile('destination_detail')) {
            $file = $request->file('destination_detail');
            $destinationPdfPath = media($file, 'storage/destinationPdf');
            $destinationPdfPath = basename($destinationPdfPath);
            $destinationData->destination_pdf = $destinationPdfPath;
        }
        $bookingData->save();
        $destinationData->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->booking_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);
        $paymentLink->save();
        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.booking_advance_payment_template',
            'to' => $userData->email,
            'subject' => 'Destination Booking Advance Payment Link',
            'data' => [
                'reply' => $request->message,
                'tickets' => $bookingData->tickets,
                'destination_price_per_perosn' => $request->per_pserson_price,
                'destination_price' => $request->total_price,
                'extras_price' => $request->extras_total_price,
                'num_of_seperate_room' => $bookingData->seperate_room,
                'single_seperate_room_price' => $request->single_room_price,
                'seperate_room_total_price' => $request->seperate_room_total_price,
                'total_price' => $request->final_payable_price,
                'advance_percentage' => $request->advance_percentage,
                'advance_price' => $request->advance_price,
                'destination_detail' => $destinationData->destination_pdf,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $bookingData->id, 'user_id' => $bookingData->user_id, 'price' => $request->advance_price, 'payment_type' => 'advance', 'trip_type' => 'booking'])),

            ],
        ]);

        return response()->json($responce, 200);
    }

    public function bookingRemainingPayment(request $request, $id)
    {
        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $userData = User::where('id', $bookingData->user_id)->first();
        $destinationData = Destinations::where('id', $bookingData->destination_id)->first();

        $bookingData->status = 'remainingPaymentPending';

        $bookingData->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->booking_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);

        //$paymentLink->expiry_at = now()->addMinutes(5);
        $paymentLink->save();

        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.booking_trip_remaining_payment_template',
            'to' => $userData->email,
            'subject' => 'Remaining Payment Link',
            'data' => [
                'reply' => $request->message,
               
                'tickets' => $bookingData->tickets,
                'destination_price_per_perosn' => $request->per_pserson_price,
                'destination_price' => $request->total_price,
                'extras_price' => $request->extras_total_price,
                'num_of_seperate_room' => $bookingData->seperate_room,
                'single_seperate_room_price' => $request->single_room_price,
                'seperate_room_total_price' => $request->seperate_room_total_price,
                'total_price' => $request->final_payable_price,
                'advance_percentage' => $request->advance_percentage,
                'advance_price' => $request->advance_price,
                'remaining_price' => $request->remaining_price,
                'destination_detail' => $destinationData->destination_pdf,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $bookingData->id, 'user_id' => $bookingData->user_id, 'price' => $request->remaining_price, 'payment_type' => 'remaining', 'trip_type' => 'booking'])),

            ],
        ]);

        return response()->json($responce, 200);
    }

    public function bookingCompletePayment(request $request, $id)
    {
        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $userData = User::where('id', $bookingData->user_id)->first();
        $destinationData = Destinations::where('id', $bookingData->destination_id)->first();

        $bookingData->status = 'completePaymentPending';

        $bookingData->save();

        // Generate a unique token for the payment link
        $token = Str::random(32);
        // Store the token, ID, email, price, and expiry timestamp in your database
        $paymentLink = new PaymentLinks();
        $paymentLink->token = $token;
        $paymentLink->booking_id = $id;
        $paymentLink->expiry_at = now()->addHours(24);

        //$paymentLink->expiry_at = now()->addMinutes(5);
        $paymentLink->save();

        $bookingData = DestinationBooking::where('id', $request->id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.booking_trip_complete_payment_template',
            'to' => $userData->email,
            'subject' => 'Booked Destination Payment Link',
            'data' => [
                'reply' => $request->message,
                
                'tickets' => $bookingData->tickets,
                'destination_price_per_perosn' => $request->per_pserson_price,
                'destination_price' => $request->total_price,
                'extras_price' => $request->extras_total_price,
                'num_of_seperate_room' => $bookingData->seperate_room,
                'single_seperate_room_price' => $request->single_room_price,
                'seperate_room_total_price' => $request->seperate_room_total_price,
                'final_payable_price' => $request->final_payable_price,
                
                'destination_detail' => $destinationData->destination_pdf,
                'payment_link' => url('/destination-booking/payment') . '?token=' . $token . '&data=' . base64_encode(json_encode(['id' => $bookingData->id, 'user_id' => $bookingData->user_id, 'price' => $request->final_payable_price, 'payment_type' => 'complete', 'trip_type' => 'booking'])),

            ],
        ]);

        return response()->json($responce, 200);
    }


 public function submitReview(Request $request)
    {
        $reviews = new Reviews();
        $reviews->name = $request->name;
        $reviews->message = $request->message;
        
        $reviews->status= 0;
        
         if ($request->hasFile('profile_img')) {
            $file = $request->file('profile_img');
            $reviewsFile = media($file, 'storage/reviewImages');
            $reviewsFile = basename($reviewsFile);
            $reviews->image = $reviewsFile;
        }
        
        $responce = $reviews->save();
        if ($responce) {
            return response()->json($reviews, 200);
        } else {
            return response()->json($reviews, 422);
        }

    }

}
