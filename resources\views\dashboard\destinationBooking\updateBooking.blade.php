@extends("dashboard.include.layout")


@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a> / Destination / Booking / Update</a>
</div>


<form id="update_booking_detail" method="post" enctype="multipart/form-data">
    <input type="hidden" name="id" value="{{ $booking->id }}">
    <div class="d-flex mb-3 justify-content-end">
        <button type="submit" class="custom_btn_1">update</button>
    </div>

    @csrf
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Booking information</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Tickets</label>
                            <input type="number" min="0" id="tickets" class="custom_form_field" placeholder="Enter tickets"
                                name="tickets" value={{$booking->tickets}}>
                        </div>
                        
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Adults</label>
                            <input type="number" min="0" id="adultes" class="custom_form_field" placeholder="Enter total adults"
                                name="adultes" value={{$booking->adultes}}>
                        </div>
                        
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Children</label>
                            <input type="number" min="0" id="children" class="custom_form_field" placeholder="Enter total children"
                                name="children" value={{$booking->children}}>
                        </div>
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Seperate Room</label>
                            <input type="number" min="0" id="seperate_room" class="custom_form_field" placeholder="Enter rooms"
                                name="seperate_room" value={{$booking->seperate_room}}>
                        </div>
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="single_price">Single Price</label>
                            <input type="number" min="0" id="single_price" class="custom_form_field" placeholder="Enter price"
                                name="single_price" value="{{$booking->single_price}}">
                        </div>
                     
                        @if($booking->type == 'private')
                       
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Date</label>
                            <input type="date" min="0" id="date" class="custom_form_field" placeholder="Enter date"
                                name="date" value={{$booking->date}}>
                        </div>
                       @endif
                    </div>

                </div>
            </div>
        </div>

    </div>
</form>

@endsection
