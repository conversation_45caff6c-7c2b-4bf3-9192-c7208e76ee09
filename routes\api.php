<?php

use App\Http\Controllers\blogCrudController;
use App\Http\Controllers\contactController;
use App\Http\Controllers\dashboardController;
use App\Http\Controllers\destinationCrudController;
use App\Http\Controllers\profile\profileController;
use App\Http\Controllers\reportsController;
use App\Http\Controllers\subscriberController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\website\HomeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
 */

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// subscription
Route::resource('subscriptions', SubscriptionController::class)->except([
    'create', 'edit',
]);

Route::put('subscriptions/{id}/status', [SubscriptionController::class, 'status']);

// agent subscriber request
Route::post('/api/subscribe', [subscriberController::class, 'postSubscribe']);

// agent subscriber request dashboard
Route::get('/api/subscriber/list', [subscriberController::class, 'getSubscriber'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/subscribers/{id}/status', [subscriberController::class, 'status'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/subscriber/aproval/{id}/status', [subscriberController::class, 'aprovalStatus'])->middleware(['verified', 'auth', 'admin']);

Route::get('/dashboard/agent/profile/{userId}', [subscriberController::class, 'profile'])->middleware(['verified', 'auth', 'admin']);

Route::delete('/api/user/subscription/{userId}', [subscriberController::class, 'deleteUserSubscription'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/user/subscription/reject/{userId}', [subscriberController::class, 'rejectUserSubscription'])->middleware(['verified', 'auth', 'admin']);

// destination dashboard category routes
Route::get('/dashboard/destination/category/list', [destinationCrudController::class, 'destinationCategoryList'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/category/store', [destinationCrudController::class, 'destinationCategoryStore'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/destination/category/delete/{id}', [destinationCrudController::class, 'destinationCategoryDestroy'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/category/update/{id}', [destinationCrudController::class, 'destinationCategoryUpdate'])->middleware(['verified', 'auth', 'admin']);

// destination dashboard type routes
Route::get('/dashboard/destination/type/list', [destinationCrudController::class, 'destinationTypeList'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/type/store', [destinationCrudController::class, 'destinationTypeStore'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/destination/type/delete/{id}', [destinationCrudController::class, 'destinationTypeDestroy'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/type/update/{id}', [destinationCrudController::class, 'destinationTypeUpdate'])->middleware(['verified', 'auth', 'admin']);

// destination dashboard country and locations routes
Route::get('/dashboard/destination/country&locations/list', [destinationCrudController::class, 'destinationCountryLocationsList'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/country&locations/store', [destinationCrudController::class, 'destinationCountryLocationsStore'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/destination/country&locations/delete/{id}', [destinationCrudController::class, 'destinationCountryLocationsDestroy'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/destination/country&locations/update/{id}', [destinationCrudController::class, 'destinationCountryLocationsUpdate'])->middleware(['verified', 'auth', 'admin']);

// destination
Route::get('/destination/list/{id}', [destinationCrudController::class, 'index'])->middleware(['verified', 'auth', 'admin']);
Route::get('/destination/list', [destinationCrudController::class, 'index'])->middleware(['verified', 'auth', 'agent']);
Route::get('/dashboard/add-destination', [destinationCrudController::class, 'addDestinationView'])->middleware(['verified', 'auth', 'agent']);
Route::post('/api/destination/store', [destinationCrudController::class, 'store'])->middleware(['verified', 'auth', 'agent']);
Route::delete('/api/destination/delete/{id}', [destinationCrudController::class, 'destroy'])->middleware(['verified', 'auth', 'agent']);
Route::put('/api/destination/status/{id}', [destinationCrudController::class, 'status'])->middleware(['verified', 'auth', 'agent']);

Route::post('/api/destination/duplicate/{id}', [destinationCrudController::class, 'duplicateRecord'])->middleware(['verified', 'auth', 'agent']);

Route::put('/api/destination/popular/{id}', [destinationCrudController::class, 'popular'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/destination/trending/{id}', [destinationCrudController::class, 'trending'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/destination/private/{id}', [destinationCrudController::class, 'makePrivate'])->middleware(['verified', 'auth']);


Route::get('/dashboard/destination/update/{id}', [destinationCrudController::class, 'show'])->name('dashboard.destinations.update')->middleware(['verified', 'auth', 'agent']);
Route::post('/api/destination/update/{id}', [destinationCrudController::class, 'update'])->middleware(['verified', 'auth', 'agent']);
Route::delete('/dashboard/destination/gallery-image/delete', [destinationCrudController::class, 'deleteGalleryImage'])->middleware(['verified', 'auth', 'agent']);

// blog dashboard category routes
Route::get('/dashboard/blog/category/list', [blogCrudController::class, 'blogCategoryList'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/blog/category/store', [blogCrudController::class, 'categoryStore'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/blog/category/delete/{id}', [blogCrudController::class, 'categoryDestroy'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/blog/category/update/{id}', [blogCrudController::class, 'categoryUpdate'])->middleware(['verified', 'auth', 'admin']);

// blog dashboard routes
Route::get('/dashboard/blog/list', [blogCrudController::class, 'index'])->middleware(['verified', 'auth', 'admin']);
Route::get('/dashboard/blog/add', [blogCrudController::class, 'addBlog'])->name('dashboard.blogs.add')->middleware(['verified', 'auth', 'admin']);
Route::post('/api/blog/store', [blogCrudController::class, 'store'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/blog/delete/{id}', [blogCrudController::class, 'destroy'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/blog/{id}/status', [blogCrudController::class, 'status'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/blog/popular/{id}/status', [blogCrudController::class, 'popularStatus'])->middleware(['verified', 'auth', 'admin']);

Route::get('/dashboard/blog/update/{id}', [blogCrudController::class, 'show'])->name('dashboard.blogs.update')->middleware(['verified', 'auth', 'admin']);
Route::post('/api/blog/update/{id}', [blogCrudController::class, 'update'])->middleware(['verified', 'auth', 'admin']);

// blog dashboard comments routes
Route::get('/dashboard/blog/comments', [blogCrudController::class, 'commentListing'])->middleware(['verified', 'auth', 'admin']);
Route::put('/api/blog/comment/{id}/status', [blogCrudController::class, 'commentStatus'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/blog/comment/delete/{id}', [blogCrudController::class, 'commentDelete'])->middleware(['verified', 'auth', 'admin']);

// global data update route
Route::POST('/admin/global/data/update/{id}', [dashboardController::class, 'updateGlobalData'])->middleware(['verified', 'auth', 'admin']);

// admin profile update route
Route::POST('/admin/profile/update/{id}', [profileController::class, 'updateProfile'])->middleware(['verified', 'auth']);
Route::POST('/admin/profile/password/update/{id}', [profileController::class, 'updateAdminPassword'])->middleware(['verified', 'auth']);

// // admin password update
// Route::POST('/admin/global/data/update/{id}', [dashboardController::class, 'updateAdminPassword']);

// newsletter
Route::POST('/api/newsletter/submit', [HomeController::class, 'newsletterSubmit'])->middleware(['verified', 'auth', 'admin']);
Route::get('/dashboard/newsletter/list', [dashboardController::class, 'getNewsletter'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/newsletter/delete/{id}', [dashboardController::class, 'deleteNewsletter'])->middleware(['verified', 'auth', 'admin']);
// contact page
Route::POST('/api/contact/submit', [HomeController::class, 'contactSubmit']);
Route::get('/dashboard/contact/list', [contactController::class, 'getContact'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/contact/delete/{id}', [contactController::class, 'destroy'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/contact/reply/{id}', [contactController::class, 'reply'])->middleware(['verified', 'auth', 'admin']);

// custom trip delete
Route::delete('/api/custom/trip/request/delete/{id}', [dashboardController::class, 'deleteCustomTrip'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/customtrip/cancelreply/{id}', [dashboardController::class, 'customTripCancleReply'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/customtrip/acceptreply/{id}', [dashboardController::class, 'customTripAcceptReply'])->middleware(['verified', 'auth', 'admin']);
Route::post('/api/customtrip/payment/remaining/{id}', [dashboardController::class, 'customTripRemainingPayment'])->middleware(['verified', 'auth', 'admin']);

Route::post('/api/customtrip/update/{id}', [dashboardController::class, 'updatecustomTripList'])->middleware(['verified', 'auth', 'admin']);

Route::post('/api/customtrip/payment/complete/{id}', [dashboardController::class, 'customTripCompletePayment'])->middleware(['verified', 'auth', 'admin']);

//delete booking
Route::delete('/api/destination/booking/delete/{id}', [dashboardController::class, 'deleteDestinationBooking'])->middleware(['verified', 'auth', 'agent']);

// booking payment
Route::post('/api/booking/destination/advance/{id}', [dashboardController::class, 'bookingAdvancePayment'])->middleware(['verified', 'auth', 'agent']);
Route::post('/api/booking/destination/remaining/{id}', [dashboardController::class, 'bookingRemainingPayment'])->middleware(['verified', 'auth', 'agent']);
Route::post('/api/booking/destination/complete/{id}', [dashboardController::class, 'bookingCompletePayment'])->middleware(['verified', 'auth', 'agent']);

//add pages seo route
Route::POST('/api/seo/data/submit', [dashboardController::class, 'addSeoData'])->middleware(['verified', 'auth', 'admin']);
Route::POST('/api/seo/data/update/{id}', [dashboardController::class, 'updateSeoData'])->middleware(['verified', 'auth', 'admin']);
Route::delete('/api/seo/data/delete/{id}', [dashboardController::class, 'deleteSeoData'])->middleware(['verified', 'auth', 'admin']);

//dashboard routes -> destination  includes
Route::get('/dashboard/destination/includes/list', [destinationCrudController::class, 'destinationIncludeList'])->middleware(['verified', 'auth']);
Route::post('/api/destination/includes/store', [destinationCrudController::class, 'destinationIncludeStore'])->middleware(['verified', 'auth']);
Route::delete('/api/destination/includes/delete/{id}', [destinationCrudController::class, 'destinationIncludeDestroy'])->middleware(['verified', 'auth']);
Route::post('/api/destination/includes/update/{id}', [destinationCrudController::class, 'destinationIncludeUpdate'])->middleware(['verified', 'auth']);

//dashboard routes -> destination Not includes
Route::get('/dashboard/destination/not-includes/list', [destinationCrudController::class, 'destinationNotIncludeList'])->middleware(['verified', 'auth']);
Route::post('/api/destination/notincludes/store', [destinationCrudController::class, 'destinationNotIncludeStore'])->middleware(['verified', 'auth']);
Route::delete('/api/destination/notincludes/delete/{id}', [destinationCrudController::class, 'destinationNotIncludeDestroy'])->middleware(['verified', 'auth']);
Route::post('/api/destination/notincludes/update/{id}', [destinationCrudController::class, 'destinationNotIncludeUpdate'])->middleware(['verified', 'auth']);

//dashboard routes -> destination extras includes
Route::get('/dashboard/destination/extras/list', [destinationCrudController::class, 'destinationExtrasList'])->middleware(['verified', 'auth']);
Route::post('/api/destination/extras/store', [destinationCrudController::class, 'destinationNotExtrasStore'])->middleware(['verified', 'auth']);
Route::delete('/api/destination/extras/delete/{id}', [destinationCrudController::class, 'destinationNotExtrasDestroy'])->middleware(['verified', 'auth']);
Route::post('/api/destination/extras/update/{id}', [destinationCrudController::class, 'destinationNotExtrasUpdate'])->middleware(['verified', 'auth']);

//dashboard routes -> destination booking report filter
Route::get('/api/destination/booking/report', [reportsController::class, 'filterBookingReport'])->name('destination.booking.report')->middleware(['verified', 'auth', 'agent']);

//dashboard routes -> get destination on agent select
Route::get('/api/destination/get/{id}', [reportsController::class, 'getDestination']);

//dashboard routes -> get destination dates on destination select
Route::get('/api/destination/dates/{id}', [reportsController::class, 'getDestinationDates']);

// add destination slots managment
Route::get('/dashboard/destination/detail/{id}', [destinationCrudController::class, 'destinationSlots'])->name('dashboard.destination.detail')->middleware(['verified', 'auth', 'agent']);
Route::post('/api/destination/slot/store', [destinationCrudController::class, 'storeDestinationSlots'])->middleware(['verified', 'auth', 'agent']);
Route::delete('/api/destination/slot/delete/{id}', [destinationCrudController::class, 'deleteDestinationSlots'])->middleware(['verified', 'auth', 'agent']);

// website search route
Route::get('/destination/search', [HomeController::class, 'liveSearch'])->name('destination.liveSearch');


Route::post('/api/payment/update/{id}', [destinationCrudController::class, 'paymentUpdate']);


// reviews submit

Route::post('/api/review/submit', [dashboardController::class, 'submitReview']);
