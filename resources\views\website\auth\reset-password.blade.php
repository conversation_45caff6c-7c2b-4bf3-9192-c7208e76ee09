@extends('website.include.layout')
@section('title', 'Login / Register')

@section('content')

<section class="page_title_section d-block w-100 text-center float-left position-relative">
    <img src="{{asset('website/images/login_banner.png')}}" class="w-100 float-left h-100 object-fit-cover"
        alt="homebanner1" loading="lazy" />
    <div class="d-flex w-100 h-100 position-absolute top-0 left-0">
        <div class="page_title d-flex align-items-center justify-content-center text-center flex-column">
            <small>Read</small>
            <h2>Rest Password</h2>
        </div>
    </div>
</section>

<section class="login_section display-block float-left w-100">

    <div class="custom_container">
        <div class="login_section_parent">
            <!-- banner slider -->
            <div id="bannerSlider" class="carousel login_slider slide w-100" data-bs-ride="carousel">
                <div class="carousel-indicators banner_dots">
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="0" class="active"
                        aria-current="true" aria-label="Slide 1"></button>
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="1"
                        aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="2"
                        aria-label="Slide 3"></button>
                </div>
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <img src="{{asset('website/images/login1.png')}}" class="d-block w-100" alt="slide1">
                    </div>
                    <div class="carousel-item">
                        <img src="{{asset('website/images/login1.png')}}" class="d-block w-100" alt="slide2">
                    </div>
                    <div class="carousel-item">
                        <img src="{{asset('website/images/login1.png')}}" class="d-block w-100" alt="slide3">
                    </div>
                </div>
            </div>

            <div class="login_section_box d-grid w-100 align-items-center">
                <div class="login_tabs_main  d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                    <div class="login_section_box_heading d-grid w-100">
                        <h3>Reset Password</h3>
                        <p>Your new password must be different from previously used passwords
                        </p>
                    </div>

                    <form method="POST" id="reset_request" class="w-100">
                        @csrf
                        <input type="hidden" name="token" value="{{ $request->route('token') }}">
                        <input type="hidden" placeholder="Enter your email" id="email" name="email"
                            value="{{$request->email}}">
                        <label class="error" generated="true" for="email"></label>
                        <div class="login_form_content_box">
                            <div class="login_form_field_box">
                                <div class="login_single_field_box">
                                    <span>New Password</span>
                                    <input id="password" class="password" type="password" placeholder="Enter password"
                                        name="password" autocomplete>
                                    <i class="far fa-eye-slash"></i>
                                    <label class="error" generated="true" for="password"></label>
                                </div>

                            </div>
                            <div class="login_form_field_box">
                                <div class="login_single_field_box">
                                    <span>Confirm Password</span>
                                    <input type="password" class="password" placeholder="Enter password"
                                        id="password_confirmation" name="password_confirmation" autocomplete>
                                    <i class="far fa-eye-slash"></i>
                                    <label class="error" generated="true" for="password_confirmation"></label>
                                </div>

                            </div>
                            <div class="login_form_signin_btn">
                                <button>Rest Password</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</section>



@endsection
