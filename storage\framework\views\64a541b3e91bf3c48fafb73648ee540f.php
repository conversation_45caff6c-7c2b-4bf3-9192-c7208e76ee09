<style>
.dropdown-menu {
    padding: .7rem 0rem;
    font-size: .875rem;
    line-height: 22px;
    color: #5c5776;
    border: none;
    box-shadow: 0 10px 30px 0 rgba(31, 45, 61, 0.1);
    border-radius: .5rem;

}

.dropdown-menu {
    display: block;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease-in;
    -o-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
}

.dropdown {
    list-style: none;

    &:hover {
        >.dropdown-menu {
            -webkit-transform: scaleY(1);
            -ms-transform: scaleY(1);
            transform: scaleY(1);
            opacity: 1;
            visibility: visible;
        }
    }
}

@media (min-width: 990px) {

    .dropright-lg {
        position: relative;

        .dropdown-menu {
            top: 0;
            right: auto;
            left: 100%;
            margin-top: 0;
            margin-right: 0.125rem;
        }
    }
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: ">";
    border-top: 0rem;
    border-right: 0rem;
    border-bottom: 0;
    border-left: 0rem;
    float: right;

}

.avatar-md {
    width: 56px;
    height: 56px;

}

.avatar img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.avatar {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 3rem;
    font-size: 1rem;
}


.avatar-online:before {
    background-color: green;
}


.avatar-indicators:before {
    content: "";
    position: absolute;
    bottom: 0px;
    right: 5%;
    width: 30%;
    height: 30%;
    border-radius: 50%;
    border: 2px solid #fff;
    display: table;

}
</style>
<section class="dashboard-header d-block w-100">
    <div class="content-container">
        <div class="dashboard_header_main d-flex justify-content-between w-100 align-items-center">
            <a class="header-hamburger-btn hamburger-btn">
                <i class="fas fa-bars"></i>
            </a>
            <ul>
                <?php if(auth()->guard()->check()): ?>

                <li class="dropdown ml-2">

                    <a class="rounded-circle " href="#" role="button" id="dropdownUser" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                        <div class="avatar avatar-md avatar-indicators avatar-online">
                            <img alt="avatar"
                                src="<?php echo e(Auth::user()->profile_image ? asset('storage/userImages/'.Auth::user()->profile_image) : asset('dashboard/img/logo.png')); ?>"
                                class="rounded-circle">
                        </div>
                    </a>

                    <div class="dropdown-menu pb-2" aria-labelledby="dropdownUser" style="right: -16px;">
                        <div class="dropdown-item">
                            <div class="d-flex py-2">
                                <div class="avatar avatar-md avatar-indicators avatar-online">
                                    <img alt="avatar"
                                        src="<?php echo e(Auth::user()->profile_image ? asset('storage/userImages/'.Auth::user()->profile_image) : asset('dashboard/img/logo.png')); ?>"
                                        class="rounded-circle">
                                </div>
                                <div class="ml-3 px-3">
                                    <h5 class="mb-0"><?php echo e(Auth::user()->name); ?></h5>
                                    <p class="mb-0"><?php echo e(Auth::user()->email); ?></p>
                                </div>

                            </div>

                        </div>
                    

                        <div class="dropdown-divider"></div>
                        <div class="">
                            <ul class="list-unstyled">
                                <li>
                                    <a class="dropdown-item" href="/dashboard/profile">
                                        <span style="margin-right: 5px;">
                                            <i class="far fa-user"></i>
                                        </span>Profile
                                    </a>
                                </li>




                            </ul>
                        </div>
                    
                        <div class="dropdown-divider"></div>
                        <ul class="list-unstyled">
                            <li>
                                <a class="dropdown-item cursor-pointer" id="logout_btn">
                                    <span style="margin-right: 5px;">
                                        <i class="far fa-power-off"></i></span>Sign Out
                                </a>
                            </li>

                        </ul>

                    </div>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</section>
<?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/include/topNav.blade.php ENDPATH**/ ?>