<?php $__env->startSection('title', 'Destination'); ?>

<?php $__env->startSection('meta_title', $destinationDetail->meta_title ?? 'Default Meta Title'); ?>
<?php $__env->startSection('meta_keywords', $destinationDetail->meta_keywords ?? 'Default Meta Keywords'); ?>
<?php $__env->startSection('meta_description', $destinationDetail->meta_description ?? 'Default Meta Description'); ?>
<?php $__env->startSection('h1', $destinationDetail->page_h1_heading ?? 'Default H1 Heading'); ?>


<?php $__env->startSection('content'); ?>


<?php
// echo "<pre>";
//echo $destinationDetail->flag;
// print_r($subscriber)
?>


<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('<?php echo url('storage/destinations/' . $destinationDetail->banner_image); ?>');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">
                <p>Travel with us</p>
                <h2><?php echo e($destinationDetail->title); ?></h2>
            </div>
        </div>
    </div>
</section>


<style>
    .inner_banner_text_section {
        margin: 0px;
    }

    .breadcrumb_list {
        padding: 0px;
        margin: 0px;
    }

    .breadcrumb_parent {
        margin-bottom: 20px;
        background: #f7f7f7;
        padding: 20px 15px;
    }

    .breadcrumb_parent {
        margin-bottom: 20px;
    }

    .breadcrumb_list li a {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        color: #000000;
        position: relative;
    }

    .breadcrumb_list li i {
        font-size: 10px;
        line-height: 18px;
        color: #000000;

    }

    .breadcrumb_list li a:hover {
        color: var(--active-color);
    }

    .breadcrumb_list li:first-child {
        padding: 5px 0px;
    }

    .breadcrumb_list li {
        padding: 5px 8px;
        list-style: none;
    }

    .breadcrumb_list li.active a {
        font-weight: 500;
        color: var(--active-color);
    }

    .breadcrumb_list li a i {
        padding-right: 14px;
        font-size: 10px;
        color: #000000;
    }

    .product_detail_page_section {
        display: block;
        width: 100%;
        padding: 0px 15px;
    }

    .detail_page_container {
        max-width: 1200px;
        margin: 0px auto;
    }

    .product_detail_page_content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto;
        grid-template-areas: ". .";
        gap: 30px 30px;
        width: 100%;
    }

    .product_detail_page_figure_box {
        float: left;
        width: 100%;
    }

    .product_detail_page_figure_box figure {
        width: 100%;
        position: relative;
        margin: 0px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FAFAFA;
    }

    .product_detail_page_figure_box figure img {
        float: left;
        width: 100%;
        cursor: pointer;
        object-fit: cover;
        aspect-ratio: 1 / 1;
    }

    /* detail page content side css start */
    .departure_detail_heading {
        font-size: 22px;
        color: #242424;
        font-weight: 600;
    }

    .departure_detail_header {
        background: #f7f7f7;
    }

    .booking_detail_body {
        background: #f7f7f7;
    }

    .booking_detail_header {
        background: #fff;
    }

    .product_detail_page_box {
        float: left;
        width: 100%;
        height: 100%;
    }

    .product_detail_page_box_section {
        display: flex;
        width: 100%;
        flex-direction: column;
    }

    .product_detail_page_box_header {
        display: flex;
        width: 100%;
        flex-direction: column;
        padding-bottom: 20px;
    }

    .product_detail_page_heading {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }

    .product_detail_page_heading h2 {
        font-size: 30px;
        font-weight: 700;
        line-height: 39px;
        color: var(--main-text-color);
        margin-bottom: 0px !important;
    }

    .product_detail_page_box_content_list {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding-bottom: 20px;
    }

    .product_detail_page_box_content_list * {
        font-size: 16px;
        font-weight: 400;
        line-height: 26px;
        color: var(--main-text-color);
    }

    .product_detail_page_box_content_list_details h6 {
        font-size: 22px;
        font-weight: 700;
        color: #242424;
        line-height: 36px;
    }

    .product_detail_page_box_content_list_details strong {
        background: #f7f7f7;
        padding: 10px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 16px;
    }

    .product_detail_page_box_content_list_details {
        padding-top: 20px;
    }

    .product_detail_page_box_extras {
        display: flex;
        border-top: 2px dashed rgba(0, 0, 0, 0.16);
        border-bottom: 2px dashed rgba(0, 0, 0, 0.16);
        margin-bottom: 25px;
        flex-wrap: wrap;
        padding-bottom: 15px;
    }

    .detail_page_box_extras_list {
        display: flex;
        align-items: center;
        padding: 15px 15px 0px 0px;
    }

    .detail_page_box_extras_list:last-child {
        padding-right: 0px;
    }

    .detail_page_box_extras_list strong {
        font-weight: 600 !important;
        font-style: normal;
        font-size: 16px;
        line-height: 19px;
        text-transform: capitalize;
        color: #242424;
        padding-right: 5px;
    }

    .detail_page_box_extras_list span {
        font-style: normal;
        font-weight: 700;
        font-size: 22px;
        line-height: 19px;
        text-transform: capitalize;
        color: #823602;
        font-family: "Ancizar Serif", serif !important;
    }


    .product_detail_page_add_to_cart_btn {
        display: flex;
        align-items: center;
        width: 100%;
        padding-bottom: 20px;
    }

    .product_detail_page_add_to_cart_btn a {
        font-size: 24px;
        font-weight: 700;
        line-height: 25px;
        color: var(--primary-text-color) !important;
        box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
        background: var(--primary-bg-color);
        border-radius: 6px;
        text-transform: capitalize;
        transition: 0.2s ease-in-out;
        height: 46px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Ancizar Serif", serif !important;
        cursor: pointer;
    }

    .product_detail_page_box_social_links {
        display: flex;
        flex-direction: column;
        padding-top: 10px;
        padding-bottom: 40px;
    }

    .product_detail_page_box_social_links h5 {
        font-style: normal;
        font-weight: 700;
        font-size: 22px;
        line-height: 29px;
        text-transform: capitalize;
        color: #242424;
        margin-bottom: 0px !important;
        padding-bottom: 15px;
    }

    .product_detail_page_socail_links_list {
        display: flex;
        align-items: center;
    }

    .product_detail_page_socail_links_list a {
        color: #ffffff;
        display: flex;
        width: 40px;
        height: 40px;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 8px;
    }

    .product_detail_page_socail_links_list a:last-child {
        margin-right: 0px;
    }

    .product_detail_page_socail_links_list a i {
        color: #ffffff;
        font-size: 20px;
    }

    .dates_listing_box {
        border-radius: 5px;
        background-color: #f7f7f7;
        padding: 10px 10px;

        position: relative;
    }



    .gallery_listing {
        display: flex;
        flex-wrap: nowrap;
        margin-top: 18px;
        overflow-x: auto;
        padding-bottom: 12px;

    }

    .gallery_listing::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .gallery_listing::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    }

    .gallery_listing::-webkit-scrollbar-thumb {
        background-color: var(--primary-bg-color);
    }

    .gallery_listing>figure {
        display: flex;
        flex: 0 0 25%;
        margin-right: 15px !important;
    }

    /* detail page content side css end */

    @media(max-width: 1024px) {}

    @media (max-width: 768px) {

        .product_detail_page_figure_box figure img {
            max-height: unset;
            height: unset;
        }

        .product_detail_page_content {
            grid-template-columns: 1fr;
            grid-template-areas: ".";
        }

        .product_detail_page_heading h2 {
            font-size: 20px;
            line-height: 26px;
        }

        .detail_page_box_extras_list {
            padding: 12px 12px 0px 0px;
        }

        .product_detail_page_box_extras {
            margin-bottom: 20px;

            padding-bottom: 12px;
        }

        .detail_page_box_extras_list strong {
            font-size: 13px;
        }

        .detail_page_box_extras_list span {
            font-size: 18px;
        }

        .product_detail_page_add_to_cart_btn a {
            font-size: 18px;
        }

        .product_detail_page_box_content_list * {
            font-size: 14px !important;
        }

        .product_detail_page_box_content_list_details h6 {
            font-size: 20px;
        }

        .product_detail_page_box_content_list_details strong {
            font-size: 14px;
        }

        ul.dates_listing li p {
            font-size: 16px;
        }

        ul.dates_listing li p small {
            font-size: 16px;
        }

        ul.dates_listing li span {

            font-size: 14px;

        }

        ul.dates_listing li span small {
            font-size: 16px;
        }

        .product_detail_page_box_social_links h5 {
            font-size: 20px;
        }

        .product_detail_page_box_social_links {
            padding-bottom: 30px;
        }

        .product_detail_page_socail_links_list a {
            width: 35px;
            height: 35px;
        }

        .product_detail_page_socail_links_list a i {
            font-size: 14px;
        }
    }
</style>
<div class="breadcrumb_parent w-100 d-block">
    <div class="custom_container">
        <ul class="breadcrumb_list d-flex align-items-center w-100 flex-wrap">
            <li class="d-flex align-items-center"><a href="/">Home</a></li>
            <li><i class="fa fa-chevron-right"></i></li>
            <li class="d-flex align-items-center"><a href="/destinations"> Destinations</a></li>
            <li><i class="fa fa-chevron-right"></i></li>
            <li class="active d-flex align-items-center">
                <a href="#"><?php echo e($destinationDetail->title); ?></a>
            </li>
        </ul>
    </div>
</div>
<section class="product_detail_page_section">
    <div class="detail_page_container">
        <div class="product_detail_page_content">
            <div class="product_detail_page_figure_box">
                <figure>
                    <img id="mainImage" src="<?php echo url('storage/destinations/' . $destinationDetail->cover_img); ?>" priority="high" alt="<?php echo e($destinationDetail->alt_text_cover_image); ?>" />
                </figure>

                <div class="gallery_listing">
                    <?php $__currentLoopData = json_decode($destinationDetail->image_gallery); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <figure class="w-100">
                        <img src="<?php echo e(asset('storage/destinations/' . $image)); ?>" onclick="updateMainImage(`<?php echo e(asset('storage/destinations/' . $image)); ?>`)" priority="high" alt="<?php echo e($destinationDetail->alt_text_cover_image); ?>"
                            class="w-100 object-fit-cover h-100" />
                    </figure>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
            </div>

            <div class="product_detail_page_box">
                <div class="product_detail_page_box_section">
                    <div class="product_detail_page_box_header">

                        <div class="product_detail_page_heading">
                            <h2><?php echo e($destinationDetail->title); ?></h2>

                        </div>
                    </div>

                    <div class="product_detail_page_box_extras">
                        <div class="detail_page_box_extras_list">
                            <?php
                            if(!empty($destinationDetail->final_price) || $destinationDetail->final_price !==null ){
                            ?>


                            <strong>Prices starting from : </strong>
                            <span><?php echo e($destinationDetail->final_price); ?> USD</span>

                            <?php
                            }
                            ?>
                        </div>
                        <div class="detail_page_box_extras_list">
                            <strong>Duration : </strong><span><?php echo e($destinationDetail->days); ?> days / <?php echo e($destinationDetail->nights); ?> nights</span>

                        </div>

                    </div>


                    <div class="product_detail_page_box_content_list">
                        <p><?php echo $destinationDetail->short_description; ?></p>
                    </div>




                    <?php if($destinationDetail->flag != 1): ?>
                    <div class="d-flex product_detail_page_add_to_cart_btn"><a data-bs-toggle="offcanvas" data-bs-target="#departureDetail" aria-controls="departureDetail"> Departure date & Availability</a> </div>

                    <div class="offcanvas offcanvas-end" tabindex="-1" id="departureDetail" aria-labelledby="departureDetailLabel">
                        <div class="offcanvas-header departure_detail_header">
                            <h5 id="departureDetailLabel" class="departure_detail_heading">Departure Details</h5>
                            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                        </div>
                        <div class="offcanvas-body">
                            <div class="product_detail_page_box_content_list_details d-grid w-100 gap-3">
                                <h6> Departure Location</h6>

                                <strong><?php echo e($destinationDetail->departure_location); ?></strong>

                            </div>
                            <div class="product_detail_page_box_content_list_details d-grid w-100 gap-3">
                                <h6> Departure Date's</h6>


                                <ul class="dates_listing">
                                    <?php if(isset($slotsData)): ?>
                                    <?php
                                    $sortedSlotsData = collect($slotsData)->sortKeysDesc();
                                    ?>
                                    <?php $__currentLoopData = $sortedSlotsData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $slots): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                    $formattedDate = customDate($date, 'F d, Y');
                                    $isExpired = now()->greaterThan(Carbon\Carbon::parse($date));
                                    ?>

                                    <li class="flex-column d-flex">
                                        <div class="dates_listing_box">

                                            <p>
                                                <?php echo e(customDate($date,'F d, Y')); ?>

                                                <small><?php if($isExpired): ?> Booking Closed <?php else: ?>
                                                    Active <?php endif; ?></small>
                                            </p>
                                            <span>Max Group Size <small>(<?php echo e($slots['slots'] ?? 0); ?>)</small> </span>
                                            <span>Available <small>
                                                    (<?php echo e(($slots['slots'] ?? 0) - ($slots['book_slots'] ?? 0)); ?>) </small> </span>
                                        </div>

                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>{
                                    <p>Not Available</p>
                                    }
                                    <?php endif; ?>
                                </ul>

                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="d-flex product_detail_page_add_to_cart_btn"><a data-bs-toggle="offcanvas" data-bs-target="#bookNow" aria-controls="bookNow">Book Now</a> </div>


                    <div class="offcanvas offcanvas-end" tabindex="-1" id="bookNow" aria-labelledby="bookNowLabel">
                        <div class="offcanvas-header booking_detail_header">
                            <h5 id="bookNowLabel" class="departure_detail_heading">Book This Tour</h5>
                            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                        </div>
                        <div class="offcanvas-body booking_detail_body">
                            <?php if($destinationDetail->flag != 1): ?>

                            <form method="POST" id="destination_booking_form" class="destination_booking_form">
                                <input type="hidden" name="destination_owner_user_id" value="<?php echo e($destinationDetail->user_id); ?>">
                                <input type="hidden" name="destination_id" value="<?php echo e($destinationDetail->id); ?>">


                                <div class="booking_heading login_section_box_heading d-grid w-100 text-center">
                                    <h3>Book This Tour</h3>

                                </div>
                                <?php echo csrf_field(); ?>
                                <div class=" login_form_content_box">

                                    <?php if(auth()->guard()->guest()): ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Name</span>
                                            <input type="text" placeholder="Enter your name" name="name">
                                            <label class="error" generated="true" for="name"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Email</span>
                                            <input type="email" placeholder="Enter your mail" name="email">
                                            <label class="error" generated="true" for="email"></label>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span style="left: 73px;z-index: 99;">phone</span>
                                            <input type="text" placeholder="Enter your number" id="mobile_code" name="phone">
                                            <i class="fas fa-phone"></i>
                                            <label class="error" generated="true" for="phone"></label>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">

                                        <span style="font-weight: 600;">Select Date</span>



                                        <ul class="form_dates_listing">
                                            <?php if(isset($slotsData)): ?>
                                            <?php
                                            $sortedSlotsData = collect($slotsData)->sortKeysDesc();
                                            ?>
                                            <?php $__currentLoopData = $sortedSlotsData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $slots): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                            $formattedDate = customDate($date, 'F d, Y');
                                            $isExpired = now()->greaterThan(Carbon\Carbon::parse($date));
                                            ?>

                                            <?php if(!$isExpired): ?>



                                            <li class=" <?php if($isExpired): ?> expire <?php endif; ?>"
                                                data-date="<?php echo e(customDate($date,'F d, Y')); ?>"
                                                data-slots="<?php echo e(($slots['slots'] ?? 0) - ($slots['book_slots'] ?? 0)); ?>">
                                                <!-- <strong style="font-size: 13px;"><?php if($isExpired): ?> Booking Closed <?php else: ?> Active
                                                    <?php endif; ?></strong> -->
                                                <p>
                                                    <?php echo e(customDate($date,'F d, Y')); ?>

                                                    <span>Slots : <?php echo e(($slots['slots'] ?? 0) - ($slots['book_slots'] ?? 0)); ?></span>
                                                </p>

                                            </li>
                                            <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                            <p>Not Available</p>
                                            <?php endif; ?>
                                        </ul>
                                        <input type="hidden" id="type" name="type" value="public">
                                        <input type="hidden" id="slots" name="slots">
                                        <input type="hidden" id="date" name="date">

                                        <label class="error" generated="true" for="date"></label>


                                    </div>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Number Of participants</span>
                                            <input type="number" min="1" max="70" placeholder="Enter number of participants" id="tickets"
                                                name="tickets">
                                            <i class="fas fa-ticket-alt"></i>
                                            <label class="error" generated="true" for="tickets"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_field_box_parent" id="total_value">
                                        <div class="login_form_field_box">
                                            <div class="booking_form_field login_single_field_box">
                                                <span>Number of adultes</span>
                                                <input type="number" min="0" max="60" placeholder="Number of Adults" id="adultes"
                                                    name="adultes" value="0">

                                                <label class="error" generated="true" for="adultes"></label>
                                            </div>
                                        </div>

                                        <div class="login_form_field_box">
                                            <div class="booking_form_field login_single_field_box">
                                                <span>Number of children</span>
                                                <input type="number" min="0" max="10" placeholder="Number of children" id="children"
                                                    name="children" value="0">

                                                <label class="error" generated="true" for="children"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="custom_select_field destination_booking d-flex flex-column">
                                            <span class="label">Addons</span>
                                            <select id="extras" class="custom_multi_select_dropdown destination_list  extras"
                                                data-control="select2" data-placeholder="Select an option" data-allow-clear="false"
                                                multiple="multiple" name="extras[]">
                                                <option></option>
                                                <?php
                                                $bookingtitlesAndPrices = [];

                                                if (!empty($destinationDetail->extras)) {
                                                $extrasArray = json_decode($destinationDetail->extras, true);
                                                $bookingtitlesAndPrices = collect($extrasArray)->map(function ($item) {
                                                [$title, $price] = explode("-", $item);
                                                return ['title' => $title, 'price' => $price];
                                                });
                                                }
                                                ?>

                                                <?php $__currentLoopData = $bookingtitlesAndPrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($item['title']); ?>-<?php echo e($item['price']); ?>">
                                                    <?php echo e($item['title']); ?> - price (<?php echo e($item['price']); ?> usd)
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <label class="error" generated="true" for="extras"></label>
                                        </div>
                                    </div>
                                    <?php
                                    if(!empty($destinationDetail->single_room_price) || $destinationDetail->single_room_price !==null ){
                                    ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Seperate Room (Optional)</span>
                                            <input type="number" min="0" max="10" placeholder="Total Number of seperate Room" id="seperate_room"
                                                name="seperate_room" value="0">

                                            <label class="error" generated="true" for="seperate_room"></label>
                                        </div>
                                        <span>Each Seperate room charged an extra mention in pricing table</span>
                                    </div>
                                    <?php
                                    }
                                    ?>



                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Message (optional)</span>
                                            <textarea name="message" col="12" row="12"></textarea>
                                            <label class="error" generated="true" for="message"></label>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="booking_form_field checkbox_field">
                                            <input type="checkbox" name="terms" id="terms">
                                            <span>Read our <a href="/booking-terms-and-conditions">Terms and Condition</a></span>
                                            <label class="error" generated="true" for="terms"></label>
                                        </div>
                                    </div>


                                    <div class="login_form_signin_btn">
                                        <button type="submit">submit</button>
                                    </div>
                                </div>
                            </form>


                            <?php else: ?>

                            <form method="POST" id="private_destination_booking_form" class="destination_booking_form">
                                <input type="hidden" name="destination_owner_user_id" value="<?php echo e($destinationDetail->user_id); ?>">
                                <input type="hidden" name="destination_id" value="<?php echo e($destinationDetail->id); ?>">


                                <div class="booking_heading login_section_box_heading d-grid w-100 text-center">
                                    <h3>Book This Tour</h3>

                                </div>
                                <?php echo csrf_field(); ?>
                                <div class=" login_form_content_box">


                                    <?php if(auth()->guard()->guest()): ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Name</span>
                                            <input type="text" placeholder="Enter your name" name="name">
                                            <label class="error" generated="true" for="name"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Email</span>
                                            <input type="email" placeholder="Enter your mail" name="email">
                                            <label class="error" generated="true" for="email"></label>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span style="left: 96px;z-index: 99;">phone</span>
                                            <input type="text" placeholder="Enter your number" id="phone_code" name="phone">
                                            <i class="fas fa-phone"></i>
                                            <label class="error" generated="true" for="phone"></label>
                                        </div>
                                    </div>

                                    <input type="hidden" id="type" name="type" value="private">
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Number Of participants</span>
                                            <input type="number" min="1" max="70" placeholder="Enter number of participants" id="tickets"
                                                name="tickets">
                                            <i class="fas fa-ticket-alt"></i>
                                            <label class="error" generated="true" for="tickets"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_field_box_parent" id="total_value">
                                        <div class="login_form_field_box">
                                            <div class="booking_form_field login_single_field_box">
                                                <span>Number of adultes</span>
                                                <input type="number" min="0" max="60" placeholder="Number of Adults" id="adultes"
                                                    name="adultes" value="0">

                                                <label class="error" generated="true" for="adultes"></label>
                                            </div>
                                        </div>

                                        <div class="login_form_field_box">
                                            <div class="booking_form_field login_single_field_box">
                                                <span>Number of children</span>
                                                <input type="number" min="0" max="10" placeholder="Number of children" id="children"
                                                    name="children" value="0">

                                                <label class="error" generated="true" for="children"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="custom_select_field destination_booking d-flex flex-column">
                                            <span class="label">Addons</span>
                                            <select id="extras" class="custom_multi_select_dropdown destination_list  extras"
                                                data-control="select2" data-placeholder="Select an option" data-allow-clear="false"
                                                multiple="multiple" name="extras[]">
                                                <option></option>
                                                <?php
                                                $bookingtitlesAndPrices = [];

                                                if (!empty($destinationDetail->extras)) {
                                                $extrasArray = json_decode($destinationDetail->extras, true);
                                                $bookingtitlesAndPrices = collect($extrasArray)->map(function ($item) {
                                                [$title, $price] = explode("-", $item);
                                                return ['title' => $title, 'price' => $price];
                                                });
                                                }
                                                ?>

                                                <?php $__currentLoopData = $bookingtitlesAndPrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($item['title']); ?>-<?php echo e($item['price']); ?>">
                                                    <?php echo e($item['title']); ?> - price (<?php echo e($item['price']); ?> usd)
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <label class="error" generated="true" for="extras"></label>
                                        </div>
                                    </div>

                                    <?php
                                    if(!empty($destinationDetail->single_room_price) || $destinationDetail->single_room_price !==null ){
                                    ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Extra Room (Optional)</span>
                                            <input type="number" min="0" max="10" placeholder="Total Number of extra Room" id="seperate_room"
                                                name="seperate_room" value="0">

                                            <label class="error" generated="true" for="seperate_room"></label>
                                        </div>
                                        <span>Each extra room charged an extra mention in pricing table</span>
                                    </div>
                                    <?php
                                    }
                                    ?>
                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Start Date</span>
                                            <input type="date" min="1" placeholder="Enter start date" id="date"
                                                name="date">
                                            <!-- <i class="fas fa-ticket-alt"></i> -->
                                            <label class="error" generated="true" for="date"></label>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="booking_form_field login_single_field_box">
                                            <span>Message (optional)</span>
                                            <textarea name="message" col="12" row="12"></textarea>
                                            <label class="error" generated="true" for="message"></label>
                                        </div>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="booking_form_field checkbox_field">
                                            <input type="checkbox" name="terms" id="terms">
                                            <span>Read our <a href="/booking-terms-and-conditions">Terms and Condition</a></span>
                                            <label class="error" generated="true" for="terms"></label>
                                        </div>
                                    </div>

                                    <div class="login_form_signin_btn">
                                        <button type="submit">submit</button>
                                    </div>
                                </div>
                            </form>

                            <?php endif; ?>
                        </div>
                    </div>


                    <div class="product_detail_page_box_social_links">
                        <h5>Share using:</h5>
                        <div class="product_detail_page_socail_links_list">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(url()->current())); ?>"
                                target="_blank"
                                style="background: #425893">
                                <i class="fab fa-facebook-f"></i>
                            </a>

                            <!-- Twitter -->
                            <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(url()->current())); ?>"
                                target="_blank"
                                style="background: #000">
                                <i style="color: #fff" class="fa-brands fa-x-twitter"></i>
                            </a>

                            <!-- LinkedIn -->
                            <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo e(urlencode(url()->current())); ?>"
                                target="_blank"
                                style="background: #3375b0">
                                <i class="fas fa-info"></i>
                            </a>

                            <!-- WhatsApp -->
                            <a href="https://wa.me/?text=<?php echo e(urlencode(url()->current())); ?>"
                                target="_blank"
                                style="background: #65d072">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // JavaScript function to update main image
    function updateMainImage(imageUrl) {
        document.getElementById('mainImage').src = imageUrl;
    }
</script>


<style>
    .checkbox_field span a {
        border-bottom: 1px solid #00000036;
        padding-bottom: 3px;
    }

    .package_detail_main_parent {
        margin-top: 40px;
        padding: 0px 15px;
    }

    .package_detail_tabs_parent {
        position: sticky;
        top: 0px;
        background: #fff;
        padding: 10px 15px;
        z-index: 9;
        box-shadow: 0px 5px 10px -15px #111;
        margin-top: 30px;
    }

    .package_detail_tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        scrollbar-width: none;

        display: flex;
        /* For Firefox */
    }

    .package_detail_tabs::-webkit-scrollbar {
        display: none;
        /* For Chrome, Safari, and Opera */
    }

    .package_detail_box {
        display: inline-block;

    }

    .package_detail_tabs li {
        flex: 0 0 16.66%;
        display: flex;
    }

    .package_detail_tabs li a {
        background-color: #fff;
        color: var(--secondary-text-color);
        font-size: 20px;
        font-weight: 700;
        line-height: 30px;
        letter-spacing: 0.1em;
        padding: 8px 10px;
        border: unset;
        white-space: nowrap;
        font-family: "Ancizar Serif", serif !important;
        border-radius: 6px;
    }

    .package_detail_tabs li a i {
        padding-right: 10px;
        font-size: 16px;
    }

    .package_detail_tabs li a.active {
        background-color: var(--primary-bg-color);
        border-bottom: 2px solid var(--primary-bg-color);
        color: #fff;
    }

    .package_detail_tabs_content_main {

        display: grid;
        gap: 40px;
    }

    .package_detail_tabs_content_main form {
        background-color: #F2F4F4;
        padding: 30px 30px;
        border: 2px solid var(--primary-bg-color);
        border-radius: 14px;
    }

    .booking_form_field input {
        background-color: var(--main-bg-color) !important;
    }

    .booking_form_field {
        height: unset;
    }

    .booking_form_field textarea {
        background-color: var(--main-bg-color) !important;
    }

    .package_detail_info_header {
        padding: 15px 15px;
        background: #f5f5f5;
        margin-bottom: 30px;
    }

    .package_detail_info_header h3 {
        font-size: 30px;
        font-weight: 700;
        line-height: 38px;
        color: #242424;
    }



    .package_detail_info_header strong {
        color: var(--active-color);
        font-size: 16px;
        font-weight: 600;
        line-height: 26px;
        text-transform: uppercase;
        padding-left: 15px;
    }

    .package_detail_info_header strong small {
        color: var(--custom-text-color-one);
        font-size: 12px;
    }

    .package_detail_info_listing {
        grid-template-columns: 1fr 1fr;
    }

    .package_detail_info_single_listing h6 {
        font-size: 24px;
        font-weight: 700;
        line-height: 34px;
        color: #242424;

    }

    .package_detail_info_single_listing ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px 10px;
    }

    .package_detail_info_single_listing ul li {
        color: #000;
        font-size: 14px;
        font-weight: 600;
        line-height: 18px;
        background: #f5f5f5;
        padding: 8px 14px;
        border-radius: 5px;
        list-style: none;
    }

    .departure_dates h6 {
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        letter-spacing: 0.1em;
        color: var(--active-color);
    }

    .departure_dates ul li {
        list-style: none;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;

    }

    .destination_gallery {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px 15px;
    }

    .destination_user_info_parent {
        display: grid;
        gap: 30px;
        max-width: 768px;
    }

    .destination_user_info figure {
        background: #f7f7f7;
        padding: 20px;
        border-radius: 6px;
    }

    .destination_user_info {
        display: grid;
        grid-template-columns: 1fr 1.5fr;
        gap: 15px 25px;
    }

    .destination_user_info figcaption h3 {
        font-size: 24px;
        font-weight: 700;
        line-height: 34px;
        color: #242424;
        margin-bottom: 15px;
    }

    .destination_user_info figcaption p {
        font-size: 16px;
        font-weight: 400;
        line-height: 26px;
        color: var(--main-text-color);
    }

    .destination_user_info figcaption p strong {
        color: var(--active-color);
    }

    .destination_user_detail p {
        font-size: 16px;
        font-weight: 400;
        line-height: 26px;
        color: var(--main-text-color);
    }

    .tour_plan_list {
        display: grid;

    }

    .single_day_details {
        display: grid;
        grid-template-columns: 1fr 85px 1fr;
        gap: 0px 10px;
        list-style: none;

    }

    .main_days_img {
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        padding: 60px 0px 60px 0px;
    }

    .main_days_img img {
        display: flex;
        width: 100%;
        align-items: center;
        height: auto;
    }

    .single_hotel_details {
        display: grid;
        grid-template-columns: 1fr 85px 1fr;
        gap: 0px 10px;
        list-style: none;
    }

    .day_heading_parent {
        position: relative;
        height: 100%;
        display: flex;
        justify-content: center;
    }

    .day_heading_parent:before {
        position: absolute;
        content: "";
        border-left: 1px dashed var(--primary-bg-color);
        height: 100%;
        left: 50%;
        width: 2px;
        display: flex;
        top: 0px;
        margin: 0px 0px 0px 0px;
    }

    .single_day_details:last-child .day_heading_parent::after {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--primary-bg-color);
        position: absolute;
        bottom: 0px;
        left: 39.5%;
        display: flex;
        content: "";
    }

    .single_day_details>li {
        padding: 56px 0px 0px 0px;
    }

    .hotel_heading_parent {
        position: relative;
        height: 100%;
        display: flex;
        justify-content: center;
    }

    .hotel_heading_parent:before {
        position: absolute;
        content: "";
        border-left: 1px dashed var(--primary-bg-color);
        height: 100%;
        left: 50%;
        width: 2px;
        display: flex;
        top: 0px;
        margin: 0px 0px 0px 0px;
    }

    .single_hotel_details>li {
        padding: 56px 0px 0px 0px;
    }

    .single_hotel_details:last-child .hotel_heading_parent::after {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--primary-bg-color);
        position: absolute;
        bottom: 0px;
        left: 39.5%;
        display: flex;
        content: "";
    }

    .single_day_details .day_heading {

        background: var(--primary-bg-color);
        height: 60px;
        width: 60px;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: var(--primary-text-color) !important;
        border-radius: 50%;
        font-family: Poppins;
        position: relative;
        text-align: center;
    }

    .single_hotel_details .day_heading {

        background: var(--primary-bg-color);
        height: 60px;
        width: 60px;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: var(--primary-text-color) !important;
        border-radius: 50%;
        font-family: Poppins;
        position: relative;
        text-align: center;
    }

    .single_day_details li * {
        width: 100% !important;
    }

    .single_day_details li * img {
        width: 100% !important;
        object-fit: cover;
    }

    .single_hotel_details li * {
        width: 100% !important;
    }

    ul.dates_listing {
        padding: 0px;
        margin: 0px;
        flex-wrap: wrap;
        display: flex;
        gap: 15px 15px;
    }



    ul.dates_listing li {
        list-style: none;
        width: 100%;
    }

    ul.dates_listing li:last-child {
        margin: 0px;
    }

    ul.dates_listing li img {
        width: 100% !important;
    }

    ul.dates_listing li p {
        margin-bottom: 10px;
        font-size: 18px;
        font-family: "Ancizar Serif", serif !important;
        background: #823602;
        font-weight: 700;
        display: flex;
        justify-content: space-between;
        color: #fff;
        padding: 10px;
        border-radius: 4px;

    }

    ul.dates_listing li p small {

        font-size: 18px;
        font-family: "Ancizar Serif", serif !important;
        font-weight: 500;
        color: #fff;
    }

    ul.dates_listing li span {
        margin-bottom: 10px;
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 15px;
        justify-content: space-between;
        color: #242424;
        font-weight: 600;
    }

    ul.dates_listing li span small {
        font-size: 18px;
        font-family: "Ancizar Serif", serif !important;
        color: #823602;
        font-weight: 700;
    }

    ul.dates_listing li span:last-child {
        margin: 0px;
    }

    ul.form_dates_listing {
        padding: 0px;
        flex-wrap: nowrap;
        overflow-x: auto;
        display: flex;
        margin-top: 12px;
    }

    ul.form_dates_listing::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ul.form_dates_listing::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    }

    ul.form_dates_listing::-webkit-scrollbar-thumb {
        background-color: var(--primary-bg-color);
    }

    ul.form_dates_listing li {
        list-style: none;
        margin-right: 15px;

    }

    ul.form_dates_listing li.expire {
        pointer-events: none;
    }

    ul.form_dates_listing li.expire {
        pointer-events: none;
    }

    ul.form_dates_listing li.expire p {
        background: #00000029;
    }


    ul.form_dates_listing li p {

        background-color: var(--main-bg-color);
        padding: 15px 15px;
        cursor: pointer;
        flex-direction: column;
        display: flex;
        white-space: nowrap;
    }

    ul.form_dates_listing li p span {

        cursor: pointer;
        padding-top: 5px;
    }

    ul.form_dates_listing li.selected p {
        background-color: var(--primary-bg-color);
        color: var(--primary-text-color);
    }

    ul.include_list {
        flex-direction: column;
        align-items: flex-start;
    }

    .include_list li {
        list-style: none;
        display: flex;
        align-items: center;
    }

    .include_list li i {
        margin-right: 10px;
        font-size: 10px;
    }

    i.fas.fa-check {
        background: green;
        color: #fff;
        border-radius: 50%;
        min-width: 20px;
        min-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    ul.not_include_list {
        flex-direction: column;
        align-items: flex-start;
    }

    ul.not_include_list li {
        list-style: none;
        display: flex;
        align-items: center;
    }


    ul.not_include_list li i {
        margin-right: 10px;
        font-size: 10px;

    }

    ul.not_include_list i.fas.fa-times {
        background: red;
        color: #fff;
        border-radius: 50%;
        border-radius: 50%;
        min-width: 20px;
        min-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

    }

    .responsive_booking_btn {
        padding: 0px 10px;
    }

    .responsive_booking_btn a {
        box-shadow: 0px 20px 35px 0px var(--primary-bg-shadow);
        background: var(--primary-bg-color);
        height: 40px;
        width: 136px;
        font-size: 15px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0em;
        color: var(--primary-text-color) !important;
        border-radius: 8px;
        margin-bottom: 15px;
        font-family: Poppins;
    }

    .package_addons span {
        font-size: 16px;
        font-weight: 600;
        line-height: 25px;
        color: var(--main-text-color);
    }

    .package_addons strong {
        color: #000;
        font-size: 12px;
        font-weight: 600;
        line-height: 25px;
        text-transform: uppercase;
        background: #f5f5f5;
        padding: 8px 14px;
        border-radius: 5px;
        margin: 0px 10px 10px 0px;
    }

    .package_addons strong small {
        color: var(--custom-text-color-one);
        font-size: 12px;
    }


    .price_table {
        display: grid;
        grid-template-columns: 1fr;
        width: 100%;

        margin-bottom: 20px;
    }

    .price_table_heading {
        display: grid;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    .price_table_heading h2 {
        justify-content: center;
        font-style: normal;
        font-size: 24px;
        font-weight: 700 !important;
        line-height: 24px;
        color: #fff;
        padding: 12px 5px;
        text-align: center;
        margin: 0px;
        word-break: break-all;
        height: 100%;
        align-items: center;
        display: flex;
        background: var(--active-color);
    }



    .price_table_detail {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
        background: #FFFFFF;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        background: #F6F6F6;
    }

    .price_table_detail span {
        color: #020202;
        margin: 0px;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        text-transform: capitalize;
        padding: 12px 12px;
        height: 100%;
        align-items: center;
        display: flex;
        border-left: 1px solid rgba(0, 0, 0, 0.12);
        word-break: break-all;
    }

    .price_table_heading h2:first-child {
        border-left: unset;
    }

    .price_table_detail span:first-child {
        border-left: unset;
    }

    .price_table_detail:nth-child(even) {
        background: #F6F6F6;
    }

    .price_table_detail:last-child {
        border: none;
    }


    @media(max-width:1024px) {


        .package_detail_tabs_content_main form {
            background-color: var(--secondary-bg-color);
            padding: 30px 20px;
        }

        .package_detail_tabs_content_main {

            grid-template-columns: 1fr;

        }



        .destination_gallery {
            grid-template-columns: 1fr 1fr;
            gap: 15px 15px;
        }

        .destination_gallery figure img {
            aspect-ratio: 1 / 1;
        }

        .package_detail_tabs li a {
            font-size: 18px;
            padding: 8px 10px;

        }

        .package_detail_tabs li a i {

            font-size: 17px;
        }


    }

    @media(max-width:768px) {
        .package_detail_info_listing {
            grid-template-columns: 1fr;
        }

        .package_detail_info_single_listing h6 {
            font-size: 20px;
            line-height: 28px;
        }

        .single_day_details {
            grid-template-columns: 1fr;

        }

        .single_day_details .day_heading_parent {
            order: 1;
            padding-bottom: 30px;
            padding-top: 30px;
            border-top: 1px dashed var(--primary-bg-color);
        }

        .single_day_details .main_days_img {
            order: 2;
            padding: 0px 0px 0px 0px;
        }

        .single_day_details>li {
            padding: 25px 0px 0px 0px;
            order: 3;
        }

        .single_day_details:first-child .day_heading_parent {
            padding-top: 0px;
            border-top: unset;
        }

        .single_day_details:last-child .day_heading_parent::after {
            display: none;
        }

        .price_table_heading h2 {

            font-size: 20px;

            line-height: 22px;
        }

        .package_detail_info_header h3 {
            font-size: 24px;
            line-height: 30px;
        }

        .single_day_details .day_heading {
            height: unset;
            width: unset;
            padding: 6px 10px;
            border-radius: 5px;
        }

        .single_hotel_details .day_heading {
            height: unset;
            width: unset;
            padding: 6px 10px;
            border-radius: 5px;
        }

        .package_detail_info_header {
            flex-direction: column;
            align-items: flex-start !important;
        }

        .package_detail_info_header strong {
            padding-top: 11px;
        }

        .destination_gallery {
            grid-template-columns: 1fr 1fr;
            gap: 10px 10px;
        }

        .package_detail_tabs li {
            flex: unset;
            margin-right: 15px;
        }

        .single_hotel_details {

            grid-template-columns: 1fr;

        }

        .single_hotel_details .hotel_heading_parent {
            order: 1;
            padding-bottom: 30px;
            padding-top: 30px;
            border-top: 1px dashed var(--primary-bg-color);
        }

        .single_hotel_details .main_days_img {
            order: 2;
            padding: 0px 0px 0px 0px;
        }

        .single_hotel_details>li {
            padding: 25px 0px 0px 0px;
            order: 3;
        }

        .single_hotel_details:first-child .hotel_heading_parent {
            padding-top: 0px;
            border-top: unset;
        }

        .single_hotel_details:last-child .hotel_heading_parent::after {
            display: none;
        }



        .tour_plan_list {
            gap: 25px 15px;
        }

        .package_detail_tabs_parent {

            margin-top: 0px;
        }

        .destination_user_info {
            grid-template-columns: 1fr;
        }
    }

    .intl-tel-input,
    .iti {
        width: 100%;
    }

    ul#iti-0__country-listbox {
        width: 280px;
    }

    .checkbox_field {
        position: relative;
        display: flex;
        align-items: center;
    }

    .checkbox_field span {
        padding-left: 7px;
        font-size: 14px;
        font-weight: normal;
    }

    .image_arrows {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 0px 10px;
    }

    .image_arrows button {
        background: var(--primary-bg-color);
        border-color: var(--primary-bg-color);
    }

    .divider_box {
        display: flex;
        width: 100%;
        border: 1px dashed var(--primary-bg-color);
        position: relative;
        margin-bottom: 40px;
    }

    .divider_box::after {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--primary-bg-color);
        position: absolute;
        left: -3px;
        display: flex;
        content: "";
        top: -11px;
    }

    .divider_box::before {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--primary-bg-color);
        position: absolute;

        right: -3px;
        display: flex;
        content: "";
        top: -11px;
    }

    @media (max-width: 540px) {
        .detail_page_box_extras_list strong {
            font-size: 12px;
        }

        .detail_page_box_extras_list span {
            font-size: 16px;
        }

        .product_detail_page_box_content_list * {
            font-size: 12px !important;
            line-height: 19px;
        }

        .product_detail_page_add_to_cart_btn a {
            font-size: 16px;
        }

        .package_detail_tabs li a {
            font-size: 16px;
            padding: 2px 10px;
        }

        .package_detail_tabs li a i {
            font-size: 14px;
        }



        .price_table_heading h2 {
            font-size: 18px;
            line-height: 22px;
        }

        .price_table_detail span {

            font-size: 12px;
        }

        .package_detail_info_single_listing h6 {
            font-size: 18px;
            line-height: 28px;
        }

        .package_detail_info_single_listing ul li {

            font-size: 12px;
        }

        .package_addons strong {

            font-size: 12px;
        }


        .product_detail_page_box_content_list_details h6 {
            font-size: 18px;
        }

        ul.dates_listing li p {

            font-size: 16px;
        }

        ul.dates_listing li p small {
            font-size: 16px;
        }

        ul.dates_listing li span {
            font-size: 14px;
        }

        ul.dates_listing li span small {
            font-size: 16px;
        }
    }

    .package_custom_box {
        display: grid;
        gap: 20px;
    }

    .package_custom_header h3 {
        font-size: 24px;
        font-weight: 600;
        line-height: 30px;
        color: var(--main-text-color);
        margin-bottom: 0px !important;
        font-family: 'Poppins', sans-serif !important;
    }

    .package_custom_content {
        gap: 12px;
    }

    .package_custom_content p {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: var(--main-text-color);
        font-family: 'Poppins', sans-serif;
    }

    .package_custom_content ul {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding-left: 15px;
    }

    .package_custom_content ul li {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: var(--main-text-color);
        font-family: 'Poppins', sans-serif;
        list-style: disc;
    }
</style>


<div class="package_detail_tabs_parent">
    <div class="custom_container">

        <ul class="package_detail_tabs package_detail_tabs_wrapper">

            <li>
                <a class="w-100 d-flex align-items-center justify-content-center" data-target="tour-plan" href="#tour-plan"><i class="fas fa-calendar"></i>Tour
                    Plan</a>
            </li>
            <li>
                <a class="w-100 d-flex align-items-center justify-content-center" data-target="accomodation-plan" href="#accomodation-plan"><i class="fas fa-hotel"></i>Accomodation</a>
            </li>
            <li>
                <a class=" w-100 d-flex align-items-center justify-content-center" data-target="pricing" href="#pricing"><i class="fas fa-dollar-sign"></i>
                    Pricing</a>
            </li>
            <li>
                <a class=" w-100 d-flex align-items-center justify-content-center" data-target="inclusions" href="#inclusions"><i class="fas fa-check-double"></i>
                    Inclusions</a>
            </li>

            <li>
                <a class="w-100 d-flex align-items-center justify-content-center" data-target="gallery" href="#gallery"><i class="fas fa-images"></i>Gallery</a>
            </li>
            <li>
                <a class="w-100 d-flex align-items-center justify-content-center" data-target="offeredby" href="#offeredby"><i class="fas fa-user"></i>Offered By</a>
            </li>

        </ul>

    </div>
</div>
<section class="d-inline-block w-100 float-left package_detail_main_parent">
    <div class="custom_container">

        <div class="package_detail_main">

            <div class="package_detail_tabs_content_main">


                <div id="tour-plan" class="package_detail_box">
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Tour Plan</h3>
                    </div> -->

                    <ul class="tour_plan_list">

                        <?php if($destinationDetail->destination_long_detail !== null): ?>
                        <?php $__currentLoopData = json_decode($destinationDetail->destination_long_detail); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single_day_details">
                            <div class="main_days_img">

                                <img src="<?php echo e(!empty($detail->main_img) ? asset('storage/destinations/' . $detail->main_img) : asset('/website/images/logo.png')); ?>" alt="Day Image" loading="lazy" decoding="async" width="800" height="530" />
                            </div>
                            <div class="day_heading_parent">
                                <span class="day_heading d-flex align-items-center justify-content-center"> <?php echo e($detail->day); ?></span>
                            </div>
                            <li><?php echo $detail->content; ?></li>
                        </div>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </ul>
                </div>


                <div id="accomodation-plan" class="package_detail_box">
                    <div class="divider_box"></div>
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Accomodation</h3>
                    </div> -->

                    <ul class="tour_plan_list">

                        <?php if($destinationDetail->destination_accomodation_detail !== null): ?>
                        <?php $__currentLoopData = json_decode($destinationDetail->destination_accomodation_detail); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single_hotel_details">
                            <li><?php echo $detail->content; ?></li>
                            <div class="hotel_heading_parent"> <span class="day_heading d-flex align-items-center justify-content-center">
                                    <?php echo e($detail->day); ?></span></div>
                            <div class="main_days_img">
                                <img src="<?php echo e(!empty($detail->main_img) ? asset('storage/destinations/' . $detail->main_img) : asset('/website/images/logo.png')); ?>" alt="Room Image" loading="lazy" decoding="async" width="800" height="530" />
                            </div>


                        </div>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </ul>
                </div>


                <div id="pricing" class="package_detail_box">
                    <div class="divider_box"></div>
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Pricing</h3>
                    </div> -->
                    <div class="d-flex flex-column w-100">



                        <div class="d-flex w-100 flex-column">
                            <div class="price_table">
                                <div class="price_table_heading">
                                    <h2>Pricing Table</h2>
                                </div>


                                <?php if(!empty($destinationDetail->final_price) || $destinationDetail->final_price !==null ): ?>

                                <div class="price_table_detail">
                                    <span>Per Person</span>
                                    <span><?php echo e($destinationDetail->final_price); ?> USD</span>

                                </div>
                                <?php endif; ?>

                                <?php if($destinationDetail->flag != 1): ?>

                                <?php if(!empty($destinationDetail->two_final_price) || $destinationDetail->two_final_price !==null ): ?>

                                <div class="price_table_detail">
                                    <span>2 person</span>
                                    <span><?php echo e($destinationDetail->two_final_price); ?> USD / Each</span>

                                </div>

                                <?php endif; ?>


                                <?php if(!empty($destinationDetail->three_to_four_final_price) || $destinationDetail->three_to_four_final_price !==null ): ?>

                                <div class="price_table_detail">
                                    <span>4 person</span>
                                    <span><?php echo e($destinationDetail->three_to_four_final_price); ?> USD / Each</span>

                                </div>
                                <?php endif; ?>


                                <?php if(!empty($destinationDetail->five_to_six_final_price) || $destinationDetail->five_to_six_final_price !==null ): ?>

                                <div class="price_table_detail">

                                    <span>6 person</span>
                                    <span><?php echo e($destinationDetail->five_to_six_final_price); ?> USD / Each</span>
                                </div>
                                <?php endif; ?>


                                <?php if(!empty($destinationDetail->six_plus_final_price) || $destinationDetail->six_plus_final_price !==null ): ?>

                                <div class="price_table_detail">

                                    <span>6+ person</span>
                                    <span><?php echo e($destinationDetail->six_plus_final_price); ?> USD / Each</span>
                                </div>
                                <?php endif; ?>



                                <?php endif; ?>


                            </div>

                            <?php if(!empty($destinationDetail->single_room_price) || $destinationDetail->single_room_price !==null ): ?>

                            <div class="price_table">
                                <div class="price_table_detail">
                                    <?php if($destinationDetail->flag != 1 ): ?>
                                    <span>Seperate Room Price</span>
                                    <?php else: ?>
                                    <span>Extra Room Price</span>
                                    <?php endif; ?>
                                    <span><?php echo e($destinationDetail->single_room_price); ?> USD <?php if ($destinationDetail->flag != 1) { ?> / Each room <?php } ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>


                        <div class="package_detail_info_single_listing d-grid w-100 gap-3">
                            <h6>Addons</h6>

                            <?php if(!empty($destinationDetail->extras)): ?>

                            <?php
                            $extrasArray = json_decode($destinationDetail->extras, true); // decode as array

                            $titlesAndPrices = collect($extrasArray)->map(function ($item) {
                            [$title, $price] = explode("-", $item);
                            return ['title' => $title, 'price' => $price];
                            });
                            ?>

                            <?php if($titlesAndPrices->isNotEmpty()): ?>
                            <div class="package_addons d-flex flex-column w-100">
                                <?php $__currentLoopData = $titlesAndPrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <strong class="">
                                    <?php echo e($item['title']); ?> ( <?php echo e($item['price']); ?> <?php echo e($destinationDetail->currency); ?>

                                    <small> / price</small> )
                                </strong>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php else: ?>
                            <p>No addons available for this destination.</p>
                            <?php endif; ?>

                            <?php else: ?>
                            <p>No addons available for this destination.</p>
                            <?php endif; ?>

                        </div>

                    </div>

                </div>

                <div id="inclusions" class="package_detail_box">
                    <div class="divider_box"></div>
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Inclusions</h3>
                    </div> -->
                    <div class="d-flex flex-column w-100">





                        <div class="package_detail_info_listing d-grid w-100 gap-4">



                            <div class="package_detail_info_single_listing d-grid w-100 gap-3">
                                <h6>Included</h6>
                                <ul class='include_list'>


                                    <?php

                                    $includeItems = json_decode($destinationDetail->include_in, true) ?? [];
                                    ?>

                                    <?php $__currentLoopData = $includeItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><i class="fas fa-check"></i>
                                        <p><?php echo e($item); ?></p>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </ul>
                            </div>
                            <div class="package_detail_info_single_listing d-grid w-100 gap-3">
                                <h6>Not Included</h6>

                                <ul class='not_include_list'>


                                    <?php
                                    $notIncludeItems = json_decode($destinationDetail->not_include, true) ?? [];
                                    ?>

                                    <?php $__currentLoopData = $notIncludeItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><i class="fas fa-times"></i>
                                        <p><?php echo e($item); ?></p>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </ul>
                            </div>

                        </div>
                    </div>

                </div>


                <div id="gallery" class="package_detail_box">
                    <div class="divider_box"></div>
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Gallery</h3>
                    </div> -->
                    <div class="destination_gallery">

                        <?php $__currentLoopData = json_decode($destinationDetail->image_gallery); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <figure class="w-100">
                            <img src="<?php echo e(asset('storage/destinations/' . $image)); ?>" alt="Gallery Image" loading="lazy" decoding="async" width="800" height="530"
                                class="w-100 object-fit-cover h-100" />
                        </figure>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    </div>

                </div>


                <div id="offeredby" class="package_detail_box">
                    <div class="divider_box"></div>
                    <!-- <div class="package_detail_info_header d-flex align-items-center justify-content-between w-100">
                        <h3>Offered By</h3>
                    </div> -->
                    <div class="destination_user_info_parent">

                        <?php if($user): ?>
                        <div class="destination_user_info">

                            <figure class="d-flex w-100 h-100 ">
                                <?php if($user && $user->profile_image): ?>
                                <img class="w-100 h-100 object-fit-cover" src="<?php echo e(asset('storage/userImages/' . $user->profile_image)); ?>"
                                    alt="Company Logo"
                                    style="width: 80px; height: 80px; object-fit: cover; border-radius: 10px;">
                                <?php else: ?>
                                <img class="w-100 h-100 object-fit-cover" src="<?php echo e(asset('website/images/logo.webp')); ?>"
                                    width="160"
                                    height="55"
                                    class="object-fit-contain w-100"
                                    alt="Default Logo"
                                    decoding="async" loading="lazy" />
                                <?php endif; ?>
                            </figure>
                            <figcaption class="d-flex flex-column">

                                <h3><?php echo e($destinationDetail->title); ?> by <?php echo e($user->name); ?></h3>
                                <div class="d-flex flex-column gap-2">
                                    <h5><b>About Your Local Guide:</b></h5>
                                    <p><?php echo e($subscriber->guide_detail ? $subscriber->guide_detail : ""); ?></p>
                                </div>
                            </figcaption>
                        </div>
                        <div class="destination_user_detail">
                            <div class="d-flex flex-column gap-2">
                                <h5>Why Travel with <?php echo e($user->name); ?>?</h5>
                                <?php
                                $agent_points = json_decode($subscriber->agent_points, true) ?? [];
                                ?>

                                <?php if(is_array($agent_points) && count($agent_points) > 0): ?>
                                    <ul>
                                        <?php $__currentLoopData = $agent_points; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $point): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <i class="fas fa-check"></i>
                                                <?php echo e($point['value'] ?? ''); ?>

                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                <?php else: ?>
                                    <p>No agent points found.</p>
                                <?php endif; ?>
                            </div>
                            <p><?php echo e($subscriber->company_description); ?></p>
                        </div>
                        <?php endif; ?>

                    </div>

                </div>
            </div>
        </div>

    </div>
</section>




<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="image-container position-relative">
                    <div class="image_arrows">
                        <button id="prevImage" class="btn btn-primary arrows"><i class="fa fa-angle-left"></i></button>
                        <button id="nextImage" class="btn btn-primary arrows"><i class="fa fa-angle-right"></i></button>
                    </div>
                    <img id="modalImage" src="" alt="Image" class="w-100">

                </div>
            </div>
        </div>
    </div>
</div>


<?php if($relatedDestinations->count() > 0): ?>
<div class="related_destination_box d-inline-block w-100">
    <div class="custom_container">
        <div class="related_destination_header d-flex w-100 align-items-center justify-content-center w-100">
            <h4>Related Destinations</h4>
        </div>
        <ul class="related_destination_list related_destination_slider">

            <?php echo $__env->make('website.include.relatedDestination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        </ul>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('website.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/website/destinationDetail.blade.php ENDPATH**/ ?>