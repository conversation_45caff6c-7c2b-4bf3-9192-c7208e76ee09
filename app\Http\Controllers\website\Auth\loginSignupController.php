<?php

namespace App\Http\Controllers\website\Auth;

use App\Http\Controllers\Controller;
use App\Models\pagesSeo;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;

class loginSignupController extends Controller
{

    public function view()
    {
        $seoData = pagesSeo::where('page', 'login')->first();
        return view('website.auth.loginSignup', [
            'seoData' => $seoData,

        ]);

    }

    public function loginRequest(Request $request)
    {
        // dd($request->profile_img);

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $credentials = $validator->validated();
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return response()->json(['message' => 'User does not exist'], 404);
        }
        // Check if the user's password is correct
        if (!Auth::attempt($credentials)) {
            return response()->json(['error' => 'Incorrect password'], 402);
        }
        // Check if the user is verified

        // if ($user->is_verified != 1 || $user->email_verified_at === null) {
        //     Auth::logout();
        //     return response()->json(['message' => 'Email not verified'], 403);

        // }
        else {

            $request->session()->regenerate();
            return response()->json(['message' => 'Login successful', 'route' => route('home')], 200);
        }
    }

    public function register(Request $request)
    {
        // dd($request->profile_img);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|string|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'profile_img' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = new User();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->role = 'user';

        // Handle image upload
        if ($request->hasFile('profile_img')) {
            $file = $request->file('profile_img');

            $profileImagePath = media($file, 'storage/userImages');
            $profileImagePath = basename($profileImagePath);

            $user->profile_image = $profileImagePath;
        }

        $user->save();

        event(new Registered($user));

        Auth::login($user);

        return response()->json(["message" => "Successfully signed up! Please check your email for a verification message. Don't forget to check your spam folder.", "status" => "success", 'route' => route('home')]);

        // $user->sendEmailVerificationNotification();

    }

    public function reset(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['message' => 'Email not found'], 404);
        }

        $response = Password::sendResetLink($request->only('email'));

        return $response == Password::RESET_LINK_SENT
        ? response()->json(['message' => 'Reset link sent to your email'], 200)
        : response()->json(['message' => 'Unable to send reset link'], 400);
    }
    public function restView(Request $request)
    {
        return view('website.auth.reset-password', ['request' => $request]);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:8',
            'password_confirmation' => 'required|same:password',
        ]);

        $user = User::where('email', $request->email)->first();

        if (Hash::check($request->password, $user->password)) {
            return response()->json(['message' => 'Password must be different from the previous one'], 403);
        }

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->password = Hash::make($request->password);
        $user->save();

        Auth::logout();
        return response()->json(['message' => 'Password reset successful', 'route' => route('login')], 200);
    }

    // public function comming()
    // {
    //   return view('front.comming_soon');
    //}

}
