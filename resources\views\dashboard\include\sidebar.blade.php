  <div class="dashboard-sidebar">
    <div class="dashboard-logo d-flex align-items-center w-100 justify-content-between">
        <img src="{{asset('dashboard/img/logo.png')}}" class="object-fit-contain" alt="Logo" loading="lazy" />
        <a class="sidebar_cross_btn hamburger-btn d-flex align-items-center justify-content-center">
            <i class="fas fa-times"></i>
        </a>
    </div>
    <div class="d-grid w-100 gap-3 dashboard-sidebar-data">

        @php($user = auth()->user())

        <div class="menu-list-box">
            <div class="menu-list-header open">
                <span>Dashboard</span>
            </div>
            <ul class="menu-list">
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/home') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/home">Dashboard</a></li>
                @if($user->role == 'admin')
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/website/settings') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/website/settings">Website Settings</a></li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/commission/setting') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/commission/setting">Commission Setting</a></li>        
                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/website/seo/settings') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/website/seo/settings">Seo Settings</a>
                </li>
                 <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/reviews/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/reviews/list">Reviews</a>
                </li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/packages') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/packages">Package Managment</a></li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/agents') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/agents">Agent Managment</a></li>
                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/custom/trip/request/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/custom/trip/request/list">Custom Trip Request's</a>
                </li>
                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/custom/trip/payment/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/custom/trip/payment/list">Custom Trip Payment's</a>
                </li>
                
                @endif
            </ul>

        </div>

   
        <div class="menu-list-box">
            <div class="menu-list-header open">
                <span>Destination Feature & Extras</span>
            </div>
            <ul class="menu-list">

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/includes/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/includes/list">Destination Includes List</a>
                </li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/not-includes/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/not-includes/list">Destination Not Includes List</a>
                </li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/extras/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/extras/list">Destination Extras List</a>
                </li>


            </ul>

        </div>
      

        <div class="menu-list-box">
            <div class="menu-list-header open">
                <span>Destination Managment</span>
            </div>
            <ul class="menu-list">
                @if($user->role == 'admin')
                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/category/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/category/list">Category Listing</a>
                </li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/type/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/type/list">Type Listing</a>
                </li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/country&locations/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/country&locations/list">Country & Location Listing</a>
                </li>
                @endif
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/destination/list') ? 'class="active"' : ''; ?>><a
                        href="/destination/list">Destination Listing</a></li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/add-destination') ? 'class="active"' : ''; ?>><a
                        href="/dashboard/add-destination">Add Destination</a></li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/destination/booking/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/destination/booking/list">Booking Detail</a>
                </li>

                <li
                    <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/booking/trip/payment/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/booking/trip/payment/list">Booking Payment Detail</a>
                </li>

                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/reports') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/reports">Booking Report</a>
                </li>
            </ul>

        </div>
        @if($user->role == 'admin')
        <div class="menu-list-box">
            <div class="menu-list-header open">
                <span>Blog Managment</span>
            </div>
            <ul class="menu-list">
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/blog/category/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/blog/category/list">Category Listing</a>
                </li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/blog/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/blog/list">Blog Listing</a>
                </li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/blog/add') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/blog/add">Add Blog</a>
                </li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/blog/comments') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/blog/comments">Blog Comments</a>
                </li>

            </ul>

        </div>
        @endif
        @if($user->role == 'admin')
        <div class="menu-list-box">
            <div class="menu-list-header open">
                <span>Contact & Newsletter</span>
            </div>
            <ul class="menu-list">
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/contact/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/contact/list">Contact Listing</a>
                </li>
                <li <?php echo ($_SERVER['REQUEST_URI'] == '/dashboard/newsletter/list') ? 'class="active"' : ''; ?>>
                    <a href="/dashboard/newsletter/list">Newsletter Listing</a>
                </li>

            </ul>

        </div>
        @endif

    </div>


</div>