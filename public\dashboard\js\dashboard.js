  $(document).ready(function () {
    $("#update_global_data_form").validate({
        rules: {
            name: {
                required: true,
            },
            primary_email: {
                required: true,
                email: true,
            },
            newsletter_email: {
                required: true,
                email: true,
            },
            contact_email: {
                required: true,
                email: true,
            },
            facebook: {
                required: true,
            },
            tiktok: {
                required: true,
            },
            twitter: {
                required: true,
            },
            youtube: {
                required: true,
            },
            primary_phone_num: {
                required: true,
            },
            secondary_phone_num: {
                required: true,
            },
            commission: {
                required: true,
            },
            address: {
                required: true,
            },
            visible_recaptcha_sitekey: {
                required: true,
            },
            invisible_recaptcha_sitekey: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);

            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/admin/global/data/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("updated successfully!", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");

                    showAlert("An error occurred while updating.", "error");
                },
            });
        },
    });
});

$("#custom_package_date_range").flatpickr({
    altInput: true,
    altFormat: "F j, Y",
    dateFormat: "Y-m-d",
    mode: "range",
});

$(document).ready(function () {
 $("#update_custom_trip").validate({
        rules: {
            total_participant: {
                required: true,
            },
            total_adults: {
                required: true,
            },
            total_children: {
                required: true,
            },
            from_to_date: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
              let id = $(form).find('[name="id"]').val();

            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/customtrip/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX


            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Data updated.",
                        "success"
                    );
                    setTimeout(function () {
                        // window.location.href = response.route;
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        showAlert(
                            "some required fields are missing !",
                            "error"
                        );
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } 
                },
            });
        },
    });

});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_newsletter")) {
        const newsletterId = event.target.getAttribute("data-id");
        console.log(newsletterId);
        document
            .querySelector("#newsletter_confirmDeleteBtn")
            .setAttribute("data-id", newsletterId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#newsletter_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/newsletter/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("newsletter record deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_newsletter")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".category_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_trip")) {
        const tripId = event.target.getAttribute("data-id");
        console.log(tripId);
        document
            .querySelector("#customTrip_confirmDeleteBtn")
            .setAttribute("data-id", tripId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#customTrip_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/custom/trip/request/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("trip record deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_trip")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".trip_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $(".custom_trip_cancle_reply_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/customtrip/cancelreply/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasCancleReply" + id)
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(".custom_trip_accept_reply_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
                total_price: {
                    required: true,
                },
                advance_percentage: {
                    required: true,
                },
                destination_detail: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/customtrip/acceptreply/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAcceptReply" + id)
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(".custom_trip_remaining_payment_link").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/customtrip/payment/remaining/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasRemainingPayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Link sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(".custom_trip_complete_payment_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
                total_price: {
                    required: true,
                },
                advance_percentage: {
                    required: true,
                },
                destination_detail: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/customtrip/payment/complete/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasCompletePayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $("#pages-seo-form").validate({
        rules: {
            page: {
                required: true,
            },
            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/seo/data/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("added successfully!", "success");
                    // Close the offcanvas
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddPageSeo")
                    );
                    offcanvas.hide();
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddPageSeo")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("page already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showAlert(
                            "An error occurred while submitting.",
                            "error"
                        );
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_seodata")) {
        const seoDataId = event.target.getAttribute("data-id");
        console.log(seoDataId);
        document
            .querySelector("#seo_confirmDeleteBtn")
            .setAttribute("data-id", seoDataId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector("#seo_confirmDeleteBtn");
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/seo/data/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("page seo record deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_seodata")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".seo_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert("An error occurred while deleting.", "error");
            },
        });
    });
});

$(document).ready(function () {
    $(".edit_seo").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                page: {
                    required: true,
                },
                page_h1_heading: {
                    required: true,
                },
                meta_title: {
                    required: true,
                },
                meta_description: {
                    required: true,
                },
                meta_keywords: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/seo/data/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasUpdateSeo" + id)
                        );
                        offcanvas.hide();
                        showAlert("page seo updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateSeo" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("page already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(".booking_remaining_payment_link").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/booking/destination/remaining/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasRemainingPayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Link sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(".booking_complete_payment_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/booking/destination/complete/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasCompletePayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_Booking_List")) {
        const BookingId = event.target.getAttribute("data-id");
        console.log(BookingId);
        document
            .querySelector("#bookingTrip_confirmDeleteBtn")
            .setAttribute("data-id", BookingId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#bookingTrip_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/booking/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Booking record deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".booking_row")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".booking_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting Booking record.",
                    "error"
                );
            },
        });
    });
});

// booking payment link

$(document).ready(function () {
    $(".booking_advance_payment_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                console.log("click");
                let baseURL = window.location.origin;

                let apiEndpoint = "/api/booking/destination/advance/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasAdvancePayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});



$(document).ready(function () {
    $("#commission_form").validate({
        rules: {
            country: {
                required: true,
            },
            commission: {
                required: true,
            },
          
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/commission/add";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("added successfully!", "success");
                    // Close the offcanvas
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddCommission")
                    );
                    offcanvas.hide();
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddCommission")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showAlert(
                            "An error occurred while submitting.",
                            "error"
                        );
                    }
                },
            });
        },
    });
});






document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_commission")) {
        const commissionId = event.target.getAttribute("data-id");
        console.log(commissionId);
        document
            .querySelector("#commission_confirmDeleteBtn")
            .setAttribute("data-id", commissionId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#commission_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/commission/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_commission")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".commission_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting record.",
                    "error"
                );
            },
        });
    });
});



$(document).ready(function () {
    $(".edit_commission_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                country: {
                    required: true,
                },
                commission: {
                    required: true,
                },
             
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/commission/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasUpdateCommision" + id)
                        );
                        offcanvas.hide();
                        showAlert("updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateCommision" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});




document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_review")) {
        const reviewId = event.target.getAttribute("data-id");
        console.log(reviewId);
        document
            .querySelector("#review_confirmDeleteBtn")
            .setAttribute("data-id", reviewId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#review_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/review/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("review deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_review")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".review_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting review.",
                    "error"
                );
            },
        });
    });
});


// Add event listener to toggle switches
document.addEventListener("change", function (event) {
    if (event.target.classList.contains("review_switch_input")) {
        const reviewId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/review/status/${reviewId}`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});
