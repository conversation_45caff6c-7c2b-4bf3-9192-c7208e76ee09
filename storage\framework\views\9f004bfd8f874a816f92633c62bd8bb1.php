


<?php $__env->startSection("wrapper"); ?>



<div class="pagination-list d-flex w-100">
    <a> / Destination / Add Destination</a>
</div>

<?php ($user = auth()->user()); ?>


<form id="add_destination_form" method="post" enctype="multipart/form-data">
    <input type="hidden" value="<?php echo e($user->id); ?>" name="user_id">
    <!-- Add Product -->
    <div class="d-flex mb-3 justify-content-end">

        <button type="submit" class="custom_btn_1">Add</button>
    </div>
    <div class="image_guide_list">
        <ul>
            <li>Upload Image must be less then 1 MB (10kb or 50 kb)</li>
            <li>Upload image of same dimension for all category and banners</li>
        </ul>
    </div>

    <?php echo csrf_field(); ?>
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destination information</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="flag">Private Trips</label>
                            <select name="flag" class="custom_form_field" id="pricing_flag">
                                <option disabled selected>select status</option>
                                <option value="1">Active</option>
                                <option value="0">Disable</option>
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Title</label>
                            <input type="text" id="title" class="custom_form_field" placeholder="Enter your title"
                                name="title">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="banner_img">Banner Image (use dimension 2500px by 400px)</label>
                            <input id="banner_img" type="file" name="banner_img" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png webp" />
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_banner_image">Alt Text of Banner Image</label>
                            <input class="custom_form_field" name="alt_text_banner_image" id="alt_text_banner_image" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="cover_img">Cover Image (use dimension 400px by 400px)</label>
                            <input id="cover_img" type="file" name="cover_img" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png webp" />
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_cover_image">Alt Text of Cover Image</label>
                            <input class="custom_form_field" name="alt_text_cover_image" id="alt_text_cover_image" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="destination_pdf">Destination pdf </label>
                            <input id="destination_pdf" type="file" name="destination_pdf" class="dropify"
                                data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png pdf webp" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="short_description">Short Description</label>
                            <textarea id="destination_short_des" class="w-100" rows="25" required
                                name="short_description"></textarea>
                        </div>

                    </div>

                </div>

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Image Gallery (use dimension 400px by 400px)</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">



                        <div class="form_field_box d-grid gap-2 w-100">

                            <div class="col-sm-10">
                                <div id="multi_image_picker" class="row"></div>
                            </div>

                        </div>

                    </div>

                </div>



                <input type="hidden" id="editor-data" name="editor_data">
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destinations</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">

                            <button type="button" class="custom_btn_1" id="add-editor-btn">Add Editor</button>

                        </div>

                        <div class="d-grid gap-3 w-100" id="editors-container">



                        </div>


                    </div>

                </div>




                <input type="hidden" id="accomodation-editor-data" name="accomodation_editor_data">
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Accomodation</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">

                            <button type="button" class="custom_btn_1" id="add-accomodation-editor-btn">Add Accomodation Editor</button>

                        </div>

                        <div class="d-grid gap-3 w-100" id="accomodation_editors_container">



                        </div>


                    </div>

                </div>



            </div>
        </div>


        <div class="add_destination_box_two d-flex flex-column">
            <div class="destination_single_box">



                <!-- <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Booking Details</h5>
                    </div>
                    <div class="d-grid gap-3 w-100">
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="booking_slots" couple_>Total Slots</label>
                            <input type="number" min="0" id="booking_slots" class="custom_form_field"
                                placeholder="Enter total slots" name="booking_slots">
                        </div>
                    </div>
                </div> -->



                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Category and Type</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="category">Category</label>

                            <select id="category" name="category" class="custom_form_field">
                                <?php $__currentLoopData = $destinationCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>"><?php echo e($category->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="type">Type</label>
                            <select id="type" name="type" class="custom_form_field">
                                <?php $__currentLoopData = $destinationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type->id); ?>"><?php echo e($type->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                    </div>
                </div>



                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Pricing</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="currency">Currency</label>
                            <select id="currency" class="custom_form_field" name="currency">
                                <option value="usd" selected>USD</option>
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="price">Single Room Price (supplementry single)</label>
                            <input type="number" min="0" id="single_room_price" class="custom_form_field" placeholder="Enter price"
                                name="single_room_price">
                        </div>

                        <?php if(!($user->role == 'admin')): ?>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="price">Price (per person)</label>
                            <input type="number" min="0" id="price" class="custom_form_field" placeholder="Enter price"
                                name="price">
                        </div>
                        <div class="group_pricing_fields">
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="two_person_price">Price (For two person's)</label>
                                <input type="number" min="0" id="two_person_price" class="custom_form_field" placeholder="Enter price"
                                    name="two_person_price">
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="three_to_four_price">Price (For three to four person's)</label>
                                <input type="number" min="0" id="three_to_four_price" class="custom_form_field" placeholder="Enter price"
                                    name="three_to_four_price">
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="five_to_six_price">Price (For five to six person's)</label>
                                <input type="number" min="0" id="five_to_six_price" class="custom_form_field" placeholder="Enter price"
                                    name="five_to_six_price">
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="six_plus_price">Price (For six plus person's)</label>
                                <input type="number" min="0" id="six_plus_price" class="custom_form_field" placeholder="Enter price"
                                    name="six_plus_price">
                            </div>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="admin_commision">Commission Percentage</label>
                            <input type="text" id="commission" class="custom_form_field"
                                placeholder="commission percentage" name="admin_commision"
                                value="<?php echo e($commission->commission); ?> %" readonly>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="final_price">Final Price (per person)</label>
                            <input type="number" min="0" id="final_price" class="custom_form_field"
                                placeholder="Enter price" name="final_price" readonly>
                        </div>
                        <div class="group_pricing_fields">
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="two_final_price">Final Price (For two person's)</label>
                                <input type="number" min="0" id="two_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="two_final_price" readonly>
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="three_to_four_final_price">Final Price (For three to four person's)</label>
                                <input type="number" min="0" id="three_to_four_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="three_to_four_final_price" readonly>
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="five_to_six_final_price">Final Price (For five to six person's)</label>
                                <input type="number" min="0" id="five_to_six_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="five_to_six_final_price" readonly>
                            </div>

                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="six_plus_final_price">Final Price (For six plus person's)</label>
                                <input type="number" min="0" id="six_plus_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="six_plus_final_price" readonly>
                            </div>
                        </div>
                        <script>
                            document.getElementById('price').addEventListener('input', calculateFinalPrice);
                            document.getElementById('two_person_price').addEventListener('input', calculateTwoFinalPrice);
                            document.getElementById('three_to_four_price').addEventListener('input', calculateThreeToFourFinalPrice);
                            document.getElementById('five_to_six_price').addEventListener('input', calculateFiveToSixFinalPrice);
                            document.getElementById('six_plus_price').addEventListener('input', calculateSixPlusFinalPrice);
                            document.getElementById('commission').addEventListener('input', calculateFinalPrice);

                            function calculateFinalPrice() {
                                const price = parseFloat(document.getElementById('price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateTwoFinalPrice() {
                                const price = parseFloat(document.getElementById('two_person_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('two_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateThreeToFourFinalPrice() {
                                const price = parseFloat(document.getElementById('three_to_four_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('three_to_four_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateFiveToSixFinalPrice() {
                                const price = parseFloat(document.getElementById('five_to_six_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('five_to_six_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateSixPlusFinalPrice() {
                                const price = parseFloat(document.getElementById('six_plus_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('six_plus_final_price').value = finalPrice.toFixed(2);
                            }
                        </script>

                        <?php else: ?>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="final_price">Price (per person)</label>
                            <input type="number" min="0" id="final_price" class="custom_form_field"
                                placeholder="Enter price" name="final_price">
                        </div>
                        <div class="group_pricing_fields">

                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="two_final_price"> Price (For two person's)</label>
                                <input type="number" min="0" id="two_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="two_final_price">
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="three_to_four_final_price"> Price (For three to four person's)</label>
                                <input type="number" min="0" id="three_to_four_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="three_to_four_final_price">
                            </div>
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="five_to_six_final_price"> Price (For five to six person's)</label>
                                <input type="number" min="0" id="five_to_six_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="five_to_six_final_price">
                            </div>

                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="six_plus_final_price"> Price (For six plus person's)</label>
                                <input type="number" min="0" id="six_plus_final_price" class="custom_form_field"
                                    placeholder="Enter price" name="six_plus_final_price">
                            </div>

                        </div>
                        <?php endif; ?>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="extras">Addons </label>
                            <select id="extras" class="custom_multi_select_dropdown extras" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="extras[]">
                                <option></option>
                                <?php if($destinationExtras && $destinationExtras->isNotEmpty()): ?>
                                <?php $__currentLoopData = $destinationExtras; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $extras): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($extras['title']); ?>-<?php echo e($extras['price']); ?>">
                                    <?php echo e($extras['title']); ?>-<?php echo e($extras['price']); ?>

                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>

                    </div>
                </div>


                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Package Details</h5>
                    </div>

                    <div class="form_field_box d-grid gap-2 w-100">
                        <label for="days">Total Day's</label>
                        <input type="number" id="days" class="custom_form_field" placeholder="Enter total days"
                            name="days" min="0">
                    </div>
                    <div class="form_field_box d-grid gap-2 w-100">
                        <label for="nights">Total Night's</label>
                        <input type="number" id="nights" class="custom_form_field" placeholder="Enter total nights"
                            name="nights" min="0">
                    </div>

                    <div class="d-grid gap-3 w-100">
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="include_in">Include in package</label>

                            <select id="include_in" class="include_in_package include_in" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="include_in[]">
                                <option></option>
                                <?php if($destinationInclude && $destinationInclude->isNotEmpty()): ?>
                                <?php $__currentLoopData = $destinationInclude; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $include): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($include['title']); ?>"><?php echo e($include['title']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="not_include">Not Include in package</label>

                            <select class="not_include_in_package not_include" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="not_include[]">
                                <option></option>
                                <?php if($destinationNotInclude && $destinationNotInclude->isNotEmpty()): ?>
                                <?php $__currentLoopData = $destinationNotInclude; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notInclude): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($notInclude['title']); ?>"><?php echo e($notInclude['title']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>

                    </div>

                </div>

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Departure Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="departure_location">Departure Location</label>
                            <input type="text" id="departure_location" class="custom_form_field"
                                placeholder="Enter departure location" name="departure_location">
                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="departure_date_time">Departure Date and Time</label>
                            <input type="text" class="custom_form_field" id="multiple-date-picker"
                                name="departure_date_time" placeholder="Select multiple dates">

                        </div>



                    </div>

                </div>

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destination Detail</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="destination_country">Destination's Country</label>

                            <select class="custom_form_field" name="destination_country" id="destination_country">
                                <option disabled selected>Select Country</option>
                                <?php $uniqueCountries = $destinationCountryLocation->pluck('country')->unique()->toArray(); ?>
                                <?php $__currentLoopData = $uniqueCountries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($country); ?>"><?php echo e($country); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>



                    </div>

                </div>

                <?php if($user->role == 'admin'): ?>
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>SEO Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="page_h1_heading">Seo H1 heading (55 to 60 charactor) </label>
                            <label>Total character (<span class="h1_headingCharCount"></span>) </label>
                            <textarea class="custom_form_field page_h1_heading" id="page_h1_heading" type="text"
                                name="page_h1_heading" rows="4"></textarea>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_title">Meta Title (55 to 60 charactor)</label>
                            <label>Total character (<span class="meta_titleCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_title" id="meta_title" type="text" name="meta_title"
                                rows="4"></textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_description">Meta Description (155 to 160 charactor)</label>
                            <label>Total character (<span class="meta_descriptionCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_description" id="meta_description" type="text"
                                name="meta_description" rows="4"></textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_keywords">Meta keywords (comma seperated)</label>
                            <textarea class="custom_form_field" id="meta_keywords" type="text" name="meta_keywords"
                                rows="4"></textarea>
                        </div>
                    </div>

                </div>
                <?php endif; ?>
            </div>
        </div>

    </div>
</form>


<?php $__env->stopSection(); ?>
<?php echo $__env->make("dashboard.include.layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/destinations/addDestination.blade.php ENDPATH**/ ?>